{"version": 3, "sources": ["../../../src/lib/helpers/get-online.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport dns from 'dns/promises'\n\nfunction getProxy(): string | undefined {\n  if (process.env.https_proxy) {\n    return process.env.https_proxy\n  }\n\n  try {\n    const httpsProxy = execSync('npm config get https-proxy', {\n      encoding: 'utf8',\n    }).trim()\n    return httpsProxy !== 'null' ? httpsProxy : undefined\n  } catch (e) {\n    return\n  }\n}\n\nexport async function getOnline(): Promise<boolean> {\n  try {\n    await dns.lookup('registry.yarnpkg.com')\n    return true\n  } catch {\n    const proxy = getProxy()\n    if (!proxy) {\n      return false\n    }\n\n    try {\n      const { hostname } = new URL(proxy)\n      await dns.lookup(hostname)\n      return true\n    } catch {\n      return false\n    }\n  }\n}\n"], "names": ["execSync", "dns", "getProxy", "process", "env", "https_proxy", "httpsProxy", "encoding", "trim", "undefined", "e", "getOnline", "lookup", "proxy", "hostname", "URL"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAe;AACxC,OAAOC,SAAS,eAAc;AAE9B,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,WAAW,EAAE;QAC3B,OAAOF,QAAQC,GAAG,CAACC,WAAW;IAChC;IAEA,IAAI;QACF,MAAMC,aAAaN,SAAS,8BAA8B;YACxDO,UAAU;QACZ,GAAGC,IAAI;QACP,OAAOF,eAAe,SAASA,aAAaG;IAC9C,EAAE,OAAOC,GAAG;QACV;IACF;AACF;AAEA,OAAO,eAAeC;IACpB,IAAI;QACF,MAAMV,IAAIW,MAAM,CAAC;QACjB,OAAO;IACT,EAAE,OAAM;QACN,MAAMC,QAAQX;QACd,IAAI,CAACW,OAAO;YACV,OAAO;QACT;QAEA,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAIF;YAC7B,MAAMZ,IAAIW,MAAM,CAACE;YACjB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF"}