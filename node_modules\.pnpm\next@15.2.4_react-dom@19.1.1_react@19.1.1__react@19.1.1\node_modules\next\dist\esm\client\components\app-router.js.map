{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  useCallback,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  CacheNode,\n  AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  ACTION_HMR_REFRESH,\n  ACTION_NAVIGATE,\n  ACTION_PREFETCH,\n  ACTION_REFRESH,\n  ACTION_RESTORE,\n  ACTION_SERVER_PATCH,\n  PrefetchKind,\n} from './router-reducer/router-reducer-types'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  RouterChangeByServerResponse,\n  RouterNavigate,\n} from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { useReducer, useUnwrapState } from './use-reducer'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport { useServerActionDispatcher } from '../app-call-server'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nfunction isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n  }\n}\n\n/**\n * Server response that only patches the cache and tree.\n */\nfunction useChangeByServerResponse(\n  dispatch: React.Dispatch<ReducerActions>\n): RouterChangeByServerResponse {\n  return useCallback(\n    ({ previousTree, serverResponse }) => {\n      startTransition(() => {\n        dispatch({\n          type: ACTION_SERVER_PATCH,\n          previousTree,\n          serverResponse,\n        })\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction useNavigate(dispatch: React.Dispatch<ReducerActions>): RouterNavigate {\n  return useCallback(\n    (href, navigateType, shouldScroll) => {\n      const url = new URL(addBasePath(href), location.href)\n\n      if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n        window.next.__pendingUrl = url\n      }\n\n      return dispatch({\n        type: ACTION_NAVIGATE,\n        url,\n        isExternalUrl: isExternalURL(url),\n        locationSearch: location.search,\n        shouldScroll: shouldScroll ?? true,\n        navigateType,\n        allowAliasing: true,\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useReducer(actionQueue)\n  const { canonicalUrl } = useUnwrapState(state)\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  const changeByServerResponse = useChangeByServerResponse(dispatch)\n  const navigate = useNavigate(dispatch)\n  useServerActionDispatcher(dispatch)\n\n  /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */\n  const appRouter = useMemo<AppRouterInstance>(() => {\n    const routerInstance: AppRouterInstance = {\n      back: () => window.history.back(),\n      forward: () => window.history.forward(),\n      prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n        ? // Unlike the old implementation, the Segment Cache doesn't store its\n          // data in the router reducer state; it writes into a global mutable\n          // cache. So we don't need to dispatch an action.\n          (href, options) =>\n            prefetchWithSegmentCache(\n              href,\n              actionQueue.state.nextUrl,\n              actionQueue.state.tree,\n              options?.kind === PrefetchKind.FULL\n            )\n        : (href, options) => {\n            // Use the old prefetch implementation.\n            const url = createPrefetchURL(href)\n            if (url !== null) {\n              // The prefetch reducer doesn't actually update any state or\n              // trigger a rerender. It just writes to a mutable cache. So we\n              // shouldn't bother calling setState/dispatch; we can just re-run\n              // the reducer directly using the current state.\n              // TODO: Refactor this away from a \"reducer\" so it's\n              // less confusing.\n              prefetchReducer(actionQueue.state, {\n                type: ACTION_PREFETCH,\n                url,\n                kind: options?.kind ?? PrefetchKind.FULL,\n              })\n            }\n          },\n      replace: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'replace', options.scroll ?? true)\n        })\n      },\n      push: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'push', options.scroll ?? true)\n        })\n      },\n      refresh: () => {\n        startTransition(() => {\n          dispatch({\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          })\n        })\n      },\n      hmrRefresh: () => {\n        if (process.env.NODE_ENV !== 'development') {\n          throw new Error(\n            'hmrRefresh can only be used in development mode. Please use refresh instead.'\n          )\n        } else {\n          startTransition(() => {\n            dispatch({\n              type: ACTION_HMR_REFRESH,\n              origin: window.location.origin,\n            })\n          })\n        }\n      },\n    }\n\n    return routerInstance\n  }, [actionQueue, dispatch, navigate])\n\n  useEffect(() => {\n    // Exists for debugging purposes. Don't use in application code.\n    if (window.next) {\n      window.next.router = appRouter\n    }\n  }, [appRouter])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = useUnwrapState(state)\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: appRouter,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [appRouter, cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatch({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [dispatch])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        if (redirectType === RedirectType.push) {\n          appRouter.push(url, {})\n        } else {\n          appRouter.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [appRouter])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = useUnwrapState(state)\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(window.location.href),\n          tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n        })\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [dispatch])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = useUnwrapState(state)\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      changeByServerResponse,\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [changeByServerResponse, tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={useUnwrapState(state)} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              <AppRouterContext.Provider value={appRouter}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "use", "useEffect", "useMemo", "useCallback", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_HMR_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "PrefetchKind", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "useReducer", "useUnwrapState", "default", "DefaultGlobalError", "Error<PERSON>ou<PERSON><PERSON>", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "useServerActionDispatcher", "prefetch", "prefetchWithSegmentCache", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "prefetchReducer", "pingVisibleLinks", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "useChangeByServerResponse", "dispatch", "previousTree", "serverResponse", "type", "useNavigate", "navigateType", "shouldScroll", "isExternalUrl", "locationSearch", "search", "allowAliasing", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "globalError", "searchParams", "pathname", "changeByServerResponse", "navigate", "appRouter", "routerInstance", "back", "forward", "options", "kind", "FULL", "replace", "scroll", "push", "refresh", "hmrRefresh", "router", "cache", "prefetchCache", "nd", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "errorComponent", "errorStyles", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,kBAAkB,EAClBC,gBAAgB,QACX,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,QACpB,qDAAoD;AAK3D,SACEC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,QACP,wCAAuC;AAO9C,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,UAAU,EAAEC,cAAc,QAAQ,gBAAe;AAC1D,SACEC,WAAWC,kBAAkB,EAC7BC,aAAa,QAER,mBAAkB;AACzB,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,wCAAuC;AAEzE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SAASC,yBAAyB,QAAQ,qBAAoB;AAE9D,SAASC,YAAYC,wBAAwB,QAAQ,kBAAiB;AACtE,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,eAAe,EAAEC,YAAY,QAAQ,mBAAkB;AAChE,SAASC,eAAe,QAAQ,6CAA4C;AAC5E,SAASC,gBAAgB,QAAQ,UAAS;AAE1C,MAAMC,gBAEF,CAAC;AAEL,SAASC,cAAcC,GAAQ;IAC7B,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AAEA;;;;;;CAMC,GACD,OAAO,SAASG,kBAAkBC,IAAY;IAC5C,kDAAkD;IAClD,IAAI1B,MAAMuB,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,IAAI5B,YAAYyB,OAAOH,OAAOC,QAAQ,CAACE,IAAI;IACvD,EAAE,OAAOI,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,qBAEL,CAFK,IAAIC,MACR,AAAC,sBAAmBL,OAAK,+CADrB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uEAAuE;IACvE,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAId,cAAcC,MAAM;QACtB,OAAO;IACT;IAEA,OAAOA;AACT;AAEA,SAASc,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;IAKtBzD,mBAAmB;QACjB,IAAIqD,QAAQC,GAAG,CAACI,4BAA4B,EAAE;YAC5C,+CAA+C;YAC/C,YAAY;YACZd,OAAOe,IAAI,CAACC,YAAY,GAAGC;QAC7B;QAEA,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGP;QACxC,MAAMQ,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGtB,OAAOuB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;QAC3D3D,kBAAkB,IAAIsC,IAAIN,OAAOC,QAAQ,CAACE,IAAI,OAAOiB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB3B,OAAOuB,OAAO,CAACK,SAAS,CAACP,cAAc,IAAID;QAC7C,OAAO;YACLpB,OAAOuB,OAAO,CAACM,YAAY,CAACR,cAAc,IAAID;QAChD;IACF,GAAG;QAACP;KAAe;IAEnB7D,UAAU;QACR,qEAAqE;QACrE,uEAAuE;QACvE,mEAAmE;QACnE,8DAA8D;QAC9D,IAAIyD,QAAQC,GAAG,CAACoB,2BAA2B,EAAE;YAC3CnC,iBAAiBkB,eAAekB,OAAO,EAAElB,eAAeK,IAAI;QAC9D;IACF,GAAG;QAACL,eAAekB,OAAO;QAAElB,eAAeK,IAAI;KAAC;IAEhD,OAAO;AACT;AAEA,OAAO,SAASc;IACd,OAAO;QACLC,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;IACX;AACF;AAEA;;CAEC,GACD,SAASC,0BACPC,QAAwC;IAExC,OAAOxF,YACL;YAAC,EAAEyF,YAAY,EAAEC,cAAc,EAAE;QAC/BzF,gBAAgB;YACduF,SAAS;gBACPG,MAAM/E;gBACN6E;gBACAC;YACF;QACF;IACF,GACA;QAACF;KAAS;AAEd;AAEA,SAASI,YAAYJ,QAAwC;IAC3D,OAAOxF,YACL,CAACiD,MAAM4C,cAAcC;QACnB,MAAMlD,MAAM,IAAIQ,IAAI5B,YAAYyB,OAAOF,SAASE,IAAI;QAEpD,IAAIM,QAAQC,GAAG,CAACI,4BAA4B,EAAE;YAC5Cd,OAAOe,IAAI,CAACC,YAAY,GAAGlB;QAC7B;QAEA,OAAO4C,SAAS;YACdG,MAAMnF;YACNoC;YACAmD,eAAepD,cAAcC;YAC7BoD,gBAAgBjD,SAASkD,MAAM;YAC/BH,cAAcA,uBAAAA,eAAgB;YAC9BD;YACAK,eAAe;QACjB;IACF,GACA;QAACV;KAAS;AAEd;AAEA,SAASW,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAevD,OAAOuB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAO8B,gCAAAA,aAAc9B,IAAI;IAC/B,IAAIA,MAAM;QACR6B,KAAK7B,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJ6B,gCAAAA,aAAc7B,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnC4B,KAAK5B,+BAA+B,GAAGA;IACzC;IAEA,OAAO4B;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMrB,OAAOqB,kBAAkB,OAAOA,cAAcrB,IAAI,GAAG;IAC3D,MAAMC,eACJoB,kBAAkB,OAAOA,cAAcpB,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMqB,sBAAsBrB,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,OAAO/E,iBAAiB+E,MAAMsB;AAChC;AAEA;;CAEC,GACD,SAASC,OAAO,KAQf;IARe,IAAA,EACdC,WAAW,EACXC,WAAW,EACXC,WAAW,EAKZ,GARe;IASd,MAAM,CAACtC,OAAOkB,SAAS,GAAGtE,WAAWwF;IACrC,MAAM,EAAExC,YAAY,EAAE,GAAG/C,eAAemD;IACxC,mEAAmE;IACnE,MAAM,EAAEuC,YAAY,EAAEC,QAAQ,EAAE,GAAG/G,QAAQ;QACzC,MAAM6C,MAAM,IAAIQ,IACdc,cACA,OAAOpB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACE,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5D4D,cAAcjE,IAAIiE,YAAY;YAC9BC,UAAUhF,YAAYc,IAAIkE,QAAQ,IAC9BjF,eAAee,IAAIkE,QAAQ,IAC3BlE,IAAIkE,QAAQ;QAClB;IACF,GAAG;QAAC5C;KAAa;IAEjB,MAAM6C,yBAAyBxB,0BAA0BC;IACzD,MAAMwB,WAAWpB,YAAYJ;IAC7BvD,0BAA0BuD;IAE1B;;GAEC,GACD,MAAMyB,YAAYlH,QAA2B;QAC3C,MAAMmH,iBAAoC;YACxCC,MAAM,IAAMrE,OAAOuB,OAAO,CAAC8C,IAAI;YAC/BC,SAAS,IAAMtE,OAAOuB,OAAO,CAAC+C,OAAO;YACrClF,UAAUqB,QAAQC,GAAG,CAACoB,2BAA2B,GAE7C,oEAAoE;YACpE,iDAAiD;YACjD,CAAC3B,MAAMoE,UACLlF,yBACEc,MACAyD,YAAYpC,KAAK,CAACO,OAAO,EACzB6B,YAAYpC,KAAK,CAACN,IAAI,EACtBqD,CAAAA,2BAAAA,QAASC,IAAI,MAAKzG,aAAa0G,IAAI,IAEvC,CAACtE,MAAMoE;gBACL,uCAAuC;gBACvC,MAAMzE,MAAMI,kBAAkBC;gBAC9B,IAAIL,QAAQ,MAAM;wBAURyE;oBATR,4DAA4D;oBAC5D,+DAA+D;oBAC/D,iEAAiE;oBACjE,gDAAgD;oBAChD,oDAAoD;oBACpD,kBAAkB;oBAClB7E,gBAAgBkE,YAAYpC,KAAK,EAAE;wBACjCqB,MAAMlF;wBACNmC;wBACA0E,MAAMD,CAAAA,gBAAAA,2BAAAA,QAASC,IAAI,YAAbD,gBAAiBxG,aAAa0G,IAAI;oBAC1C;gBACF;YACF;YACJC,SAAS,CAACvE,MAAMoE;oBAAAA,oBAAAA,UAAU,CAAC;gBACzBpH,gBAAgB;wBACYoH;oBAA1BL,SAAS/D,MAAM,WAAWoE,CAAAA,kBAAAA,QAAQI,MAAM,YAAdJ,kBAAkB;gBAC9C;YACF;YACAK,MAAM,CAACzE,MAAMoE;oBAAAA,oBAAAA,UAAU,CAAC;gBACtBpH,gBAAgB;wBACSoH;oBAAvBL,SAAS/D,MAAM,QAAQoE,CAAAA,kBAAAA,QAAQI,MAAM,YAAdJ,kBAAkB;gBAC3C;YACF;YACAM,SAAS;gBACP1H,gBAAgB;oBACduF,SAAS;wBACPG,MAAMjF;wBACNmC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;oBAChC;gBACF;YACF;YACA+E,YAAY;gBACV,IAAIrE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,MAAM,qBAEL,CAFK,IAAIH,MACR,iFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO;oBACLrD,gBAAgB;wBACduF,SAAS;4BACPG,MAAMpF;4BACNsC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,OAAOqE;IACT,GAAG;QAACR;QAAalB;QAAUwB;KAAS;IAEpClH,UAAU;QACR,gEAAgE;QAChE,IAAIgD,OAAOe,IAAI,EAAE;YACff,OAAOe,IAAI,CAACgE,MAAM,GAAGZ;QACvB;IACF,GAAG;QAACA;KAAU;IAEd,IAAI1D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,sDAAsD;QACtD,MAAM,EAAEqE,KAAK,EAAEC,aAAa,EAAE/D,IAAI,EAAE,GAAG7C,eAAemD;QAEtD,4FAA4F;QAC5F,sDAAsD;QACtDxE,UAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCgD,OAAOkF,EAAE,GAAG;gBACVH,QAAQZ;gBACRa;gBACAC;gBACA/D;YACF;QACF,GAAG;YAACiD;YAAWa;YAAOC;YAAe/D;SAAK;IAC5C;IAEAlE,UAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASmI,eAAeC,KAA0B;gBAG7CpF;YAFH,IACE,CAACoF,MAAMC,SAAS,IAChB,GAACrF,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,qBAApBxB,sBAAsB0B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9B9B,cAAc0F,cAAc,GAAGrE;YAE/ByB,SAAS;gBACPG,MAAMhF;gBACNiC,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;gBACjCe,MAAMlB,OAAOuB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA1B,OAAOuF,gBAAgB,CAAC,YAAYJ;QAEpC,OAAO;YACLnF,OAAOwF,mBAAmB,CAAC,YAAYL;QACzC;IACF,GAAG;QAACzC;KAAS;IAEb1F,UAAU;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAASyI,wBACPL,KAAyC;YAEzC,MAAMM,QAAQ,YAAYN,QAAQA,MAAMO,MAAM,GAAGP,MAAMM,KAAK;YAC5D,IAAIlG,gBAAgBkG,QAAQ;gBAC1BN,MAAMQ,cAAc;gBACpB,MAAM9F,MAAMP,wBAAwBmG;gBACpC,MAAMG,eAAevG,yBAAyBoG;gBAC9C,IAAIG,iBAAiBpG,aAAamF,IAAI,EAAE;oBACtCT,UAAUS,IAAI,CAAC9E,KAAK,CAAC;gBACvB,OAAO;oBACLqE,UAAUO,OAAO,CAAC5E,KAAK,CAAC;gBAC1B;YACF;QACF;QACAE,OAAOuF,gBAAgB,CAAC,SAASE;QACjCzF,OAAOuF,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACLzF,OAAOwF,mBAAmB,CAAC,SAASC;YACpCzF,OAAOwF,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG;QAACtB;KAAU;IAEd,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAEhD,OAAO,EAAE,GAAG9C,eAAemD;IACnC,IAAIL,QAAQ2E,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAIlG,cAAc0F,cAAc,KAAKlE,cAAc;YACjD,MAAMnB,YAAWD,OAAOC,QAAQ;YAChC,IAAIkB,QAAQQ,WAAW,EAAE;gBACvB1B,UAAS8F,MAAM,CAAC3E;YAClB,OAAO;gBACLnB,UAASyE,OAAO,CAACtD;YACnB;YAEAxB,cAAc0F,cAAc,GAAGlE;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/BrE,IAAI+B;IACN;IAEA9B,UAAU;QACR,MAAMgJ,oBAAoBhG,OAAOuB,OAAO,CAACK,SAAS,CAACqE,IAAI,CAACjG,OAAOuB,OAAO;QACtE,MAAM2E,uBAAuBlG,OAAOuB,OAAO,CAACM,YAAY,CAACoE,IAAI,CAC3DjG,OAAOuB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAM4E,iCAAiC,CACrCrG;gBAIEE;YAFF,MAAMG,OAAOH,OAAOC,QAAQ,CAACE,IAAI;YACjC,MAAMe,QACJlB,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,qBAApBxB,sBAAsB0B,+BAA+B;YAEvDvE,gBAAgB;gBACduF,SAAS;oBACPG,MAAMhF;oBACNiC,KAAK,IAAIQ,IAAIR,cAAAA,MAAOK,MAAMA;oBAC1Be;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDlB,OAAOuB,OAAO,CAACK,SAAS,GAAG,SAASA,UAClC0B,IAAS,EACT8C,OAAe,EACftG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIwD,CAAAA,wBAAAA,KAAM7B,IAAI,MAAI6B,wBAAAA,KAAM+C,EAAE,GAAE;gBAC1B,OAAOL,kBAAkB1C,MAAM8C,SAAStG;YAC1C;YAEAwD,OAAOD,+BAA+BC;YAEtC,IAAIxD,KAAK;gBACPqG,+BAA+BrG;YACjC;YAEA,OAAOkG,kBAAkB1C,MAAM8C,SAAStG;QAC1C;QAEA;;;;KAIC,GACDE,OAAOuB,OAAO,CAACM,YAAY,GAAG,SAASA,aACrCyB,IAAS,EACT8C,OAAe,EACftG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIwD,CAAAA,wBAAAA,KAAM7B,IAAI,MAAI6B,wBAAAA,KAAM+C,EAAE,GAAE;gBAC1B,OAAOH,qBAAqB5C,MAAM8C,SAAStG;YAC7C;YACAwD,OAAOD,+BAA+BC;YAEtC,IAAIxD,KAAK;gBACPqG,+BAA+BrG;YACjC;YACA,OAAOoG,qBAAqB5C,MAAM8C,SAAStG;QAC7C;QAEA;;;;KAIC,GACD,MAAMwG,aAAa,CAAClB;YAClB,IAAI,CAACA,MAAM5D,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAAC4D,MAAM5D,KAAK,CAACC,IAAI,EAAE;gBACrBzB,OAAOC,QAAQ,CAACsG,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;YACpEpJ,gBAAgB;gBACduF,SAAS;oBACPG,MAAMhF;oBACNiC,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;oBACjCe,MAAMkE,MAAM5D,KAAK,CAACE,+BAA+B;gBACnD;YACF;QACF;QAEA,8CAA8C;QAC9C1B,OAAOuF,gBAAgB,CAAC,YAAYe;QACpC,OAAO;YACLtG,OAAOuB,OAAO,CAACK,SAAS,GAAGoE;YAC3BhG,OAAOuB,OAAO,CAACM,YAAY,GAAGqE;YAC9BlG,OAAOwF,mBAAmB,CAAC,YAAYc;QACzC;IACF,GAAG;QAAC5D;KAAS;IAEb,MAAM,EAAEsC,KAAK,EAAE9D,IAAI,EAAEa,OAAO,EAAEyE,iBAAiB,EAAE,GAAGnI,eAAemD;IAEnE,MAAMiF,eAAexJ,QAAQ;QAC3B,OAAO4B,gBAAgBmG,OAAO9D,IAAI,CAAC,EAAE;IACvC,GAAG;QAAC8D;QAAO9D;KAAK;IAEhB,yCAAyC;IACzC,MAAMwF,aAAazJ,QAAQ;QACzB,OAAOgC,kBAAkBiC;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMyF,sBAAsB1J,QAAQ;QAClC,OAAO;YACL2J,YAAY1F;YACZ2F,iBAAiB7B;YACjB8B,mBAAmB;YACnB,6BAA6B;YAC7B,8EAA8E;YAC9EhH,KAAKsB;QACP;IACF,GAAG;QAACF;QAAM8D;QAAO5D;KAAa;IAE9B,MAAM2F,4BAA4B9J,QAAQ;QACxC,OAAO;YACLgH;YACA/C;YACAsF;YACAzE;QACF;IACF,GAAG;QAACkC;QAAwB/C;QAAMsF;QAAmBzE;KAAQ;IAE7D,IAAIK;IACJ,IAAIqE,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAAChD,eAAeuD,QAAQ,GAAGP;QACjCrE,qBAAO,KAACoB;YAAmBC,eAAeA;WAAxBuD;IACpB,OAAO;QACL5E,OAAO;IACT;IAEA,IAAI6E,wBACF,MAACrI;;YACEwD;YACA4C,MAAM9C,GAAG;0BACV,KAACvD;gBAAmBuC,MAAMA;;;;IAI9B,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kEAAkE;QAClE,iGAAiG;QACjG,iBAAiB;QACjB,8CAA8C;QAC9C,wBAAwB;QACxB,kEAAkE;QAClE,IAAI,OAAOX,WAAW,aAAa;YACjC,MAAM,EAAEkH,iCAAiC,EAAE,GACzCC,QAAQ;YACVF,wBACE,KAACC;0BACED;;QAGP;QACA,MAAMG,cACJD,QAAQ,+CAA+C7I,OAAO;QAEhE2I,wBACE,KAACG;YAAYvD,aAAaA;YAAaC,aAAaA;sBACjDmD;;IAGP,OAAO;QACL,0EAA0E;QAC1EA,wBACE,KAACzI;YACC6I,gBAAgBvD,WAAW,CAAC,EAAE;YAC9BwD,aAAaxD,WAAW,CAAC,EAAE;sBAE1BmD;;IAGP;IAEA,qBACE;;0BACE,KAACrG;gBAAeC,gBAAgBxC,eAAemD;;0BAC/C,KAAC+F;0BACD,KAACpJ,kBAAkBqJ,QAAQ;gBAACC,OAAOf;0BACjC,cAAA,KAACxI,gBAAgBsJ,QAAQ;oBAACC,OAAOzD;8BAC/B,cAAA,KAAC/F,oBAAoBuJ,QAAQ;wBAACC,OAAO1D;kCACnC,cAAA,KAACvG,0BAA0BgK,QAAQ;4BACjCC,OAAOV;sCAEP,cAAA,KAACzJ,iBAAiBkK,QAAQ;gCAACC,OAAOtD;0CAChC,cAAA,KAAC5G,oBAAoBiK,QAAQ;oCAACC,OAAOd;8CAClCM;;;;;;;;;AASnB;AAEA,eAAe,SAASS,UAAU,KAQjC;IARiC,IAAA,EAChC9D,WAAW,EACX+D,+BAA+B,CAACC,sBAAsBC,kBAAkB,EACxEhE,WAAW,EAKZ,GARiC;IAShC3E;IAEA,qBACE,KAACV;QACC,sFAAsF;QACtF,uGAAuG;QACvG6I,gBAAgB9I;kBAEhB,cAAA,KAACoF;YACCC,aAAaA;YACbC,aAAaA;YACbC,aAAa;gBAAC8D;gBAAsBC;aAAkB;;;AAI9D;AAEA,MAAMC,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAU/H,IAAY;IACjD,IAAIgI,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAAClI;IAClB,IAAI2H,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAASlB;IACP,MAAM,GAAGmB,YAAY,GAAG5L,MAAM6L,QAAQ,CAAC;IACvC,MAAMC,qBAAqBd,cAAcM,IAAI;IAC7CpL,UAAU;QACR,MAAM6L,UAAU,IAAMH,YAAY,CAACI,IAAMA,IAAI;QAC7Cd,oBAAoBK,GAAG,CAACQ;QACxB,IAAID,uBAAuBd,cAAcM,IAAI,EAAE;YAC7CS;QACF;QACA,OAAO;YACLb,oBAAoBe,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBF;KAAY;IAEpC,MAAMM,QAAQvI,QAAQC,GAAG,CAACuI,kBAAkB,GACxC,AAAC,UAAOxI,QAAQC,GAAG,CAACuI,kBAAkB,GACtC;IACJ,OAAO;WAAInB;KAAc,CAACoB,GAAG,CAAC,CAAC/I,MAAMgJ,kBACnC,KAACC;YAECC,KAAI;YACJlJ,MAAM,AAAC,KAAEA,OAAO6I;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX"}