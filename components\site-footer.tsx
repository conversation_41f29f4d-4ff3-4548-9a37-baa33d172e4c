"use client"

import { <PERSON>, Gith<PERSON>, <PERSON>, Linkedin, MessageCircle, Heart } from 'lucide-react'
import Link from "next/link"

export default function SiteFooter() {
return (
  <footer className="relative z-10 border-t border-white/10">
    <div className="glass-ultra">
      <div className="container mx-auto px-4 py-8">
        {/* Single Row Layout */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          {/* Brand Section - Compact */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-xl flex items-center justify-center">
              <img src="/cythro.png" alt="Cythro Logo" className="w-10 h-10 object-contain" />
            </div>
            <div>
              <span className="text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent">
                Cy<PERSON><PERSON>
              </span>
              <p className="text-gray-400 text-xs font-medium -mt-1">Digital Creators</p>
            </div>
          </div>

          {/* Center - Copyright */}
          <div className="flex items-center space-x-2 text-gray-400 text-sm">
            <span>Made with</span>
            <Heart className="w-3 h-3 text-red-400 fill-red-400" />
            <span>by Cythro © 2025</span>
          </div>

          {/* Right - Social Links */}
          <div className="flex items-center space-x-3">
            {[
              { icon: Github, label: "GitHub", href: "#" },
              { icon: Twitter, label: "Twitter", href: "#" },
              { icon: MessageCircle, label: "Discord", href: "#" },
            ].map((social) => (
              <Link
                key={social.label}
                href={social.href}
                className="w-8 h-8 rounded-lg glass-ultra border border-white/10 hover:bg-white/[0.08] flex items-center justify-center cursor-pointer transition-all duration-300 group shadow-glass hover:scale-110"
                aria-label={social.label}
              >
                <social.icon className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors duration-300" />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  </footer>
)
}
