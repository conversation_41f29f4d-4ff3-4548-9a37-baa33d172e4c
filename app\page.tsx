"use client"

import SiteHeader from "@/components/site-header"
import HeroSection from "@/components/hero-section"
import ServicesSection from "@/components/services-section"
import BlockmanLegacyHero from "@/components/blockman-legacy-hero"
import SiteFooter from "@/components/site-footer"

export default function UltraModernLanding() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-black">
      {/* Enhanced Background System */}
      <div className="fixed inset-0 z-0">
        {/* Base Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900"></div>

        {/* Enhanced Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 md:w-[500px] md:h-[500px] bg-gradient-radial from-[#780206]/40 via-[#780206]/20 to-transparent rounded-full blur-3xl animate-pulse-glow"></div>
        <div className="absolute bottom-1/3 right-1/4 w-[500px] h-[500px] md:w-[600px] md:h-[600px] bg-gradient-radial from-[#061161]/40 via-[#061161]/20 to-transparent rounded-full blur-3xl animate-pulse-glow" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] md:w-[800px] md:h-[800px] bg-gradient-conic from-[#780206]/30 via-purple-500/15 to-[#061161]/30 rounded-full blur-3xl animate-pulse-glow" style={{animationDelay: '2s'}}></div>

        {/* Enhanced Grid Pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-16 md:bg-grid-32"></div>
      </div>

      <SiteHeader />
      <HeroSection />
      <ServicesSection />
      <BlockmanLegacyHero />
      <SiteFooter />
    </div>
  )
}
