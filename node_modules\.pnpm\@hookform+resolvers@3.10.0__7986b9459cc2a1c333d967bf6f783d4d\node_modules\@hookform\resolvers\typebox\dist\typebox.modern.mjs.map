{"version": 3, "file": "typebox.modern.mjs", "sources": ["../src/typebox.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { TypeCheck } from '@sinclair/typebox/compiler';\nimport { Value, type ValueError } from '@sinclair/typebox/value';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (\n  _errors: ValueError[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; _errors.length; ) {\n    const error = _errors[0];\n    const { type, message, path } = error;\n    const _path = path.substring(1).replace(/\\//g, '.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message, type: '' + type };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types['' + type];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '' + type,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    _errors.shift();\n  }\n\n  return errors;\n};\n\nexport const typeboxResolver: Resolver =\n  (schema) => async (values, _, options) => {\n    const errors = Array.from(\n      schema instanceof TypeCheck\n        ? schema.Errors(values)\n        : Value.Errors(schema, values),\n    );\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    if (!errors.length) {\n      return {\n        errors: {} as FieldErrors,\n        values,\n      };\n    }\n\n    return {\n      values: {},\n      errors: toNestErrors(\n        parseErrorSchema(\n          errors,\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n        options,\n      ),\n    };\n  };\n"], "names": ["parseErrorSchema", "_errors", "validateAllFieldCriteria", "errors", "length", "error", "type", "message", "path", "_path", "substring", "replace", "types", "messages", "appendErrors", "concat", "shift", "typeboxResolver", "schema", "async", "values", "_", "options", "Array", "from", "TypeCheck", "Errors", "Value", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors", "criteriaMode"], "mappings": "qOAMA,MAAMA,EAAmBA,CACvBC,EACAC,KAEA,MAAMC,EAAqC,CAAE,EAC7C,KAAOF,EAAQG,QAAU,CACvB,MAAMC,EAAQJ,EAAQ,IAChBK,KAAEA,EAAIC,QAAEA,EAAOC,KAAEA,GAASH,EAC1BI,EAAQD,EAAKE,UAAU,GAAGC,QAAQ,MAAO,KAM/C,GAJKR,EAAOM,KACVN,EAAOM,GAAS,CAAEF,UAASD,KAAM,GAAKA,IAGpCJ,EAA0B,CAC5B,MAAMU,EAAQT,EAAOM,GAAOG,MACtBC,EAAWD,GAASA,EAAM,GAAKN,GAErCH,EAAOM,GAASK,EACdL,EACAP,EACAC,EACA,GAAKG,EACLO,EACK,GAAgBE,OAAOF,EAAsBR,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAQe,OACV,CAEA,OAAOb,GAGIc,EACVC,GAAWC,MAAOC,EAAQC,EAAGC,KAC5B,MAAMnB,EAASoB,MAAMC,KACnBN,aAAkBO,EACdP,EAAOQ,OAAON,GACdO,EAAMD,OAAOR,EAAQE,IAK3B,OAFAE,EAAQM,2BAA6BC,EAAuB,CAAE,EAAEP,GAE3DnB,EAAOC,OAOL,CACLgB,OAAQ,CAAE,EACVjB,OAAQ2B,EACN9B,EACEG,GACCmB,EAAQM,2BAAsD,QAAzBN,EAAQS,cAEhDT,IAbK,CACLnB,OAAQ,CAAiB,EACzBiB"}