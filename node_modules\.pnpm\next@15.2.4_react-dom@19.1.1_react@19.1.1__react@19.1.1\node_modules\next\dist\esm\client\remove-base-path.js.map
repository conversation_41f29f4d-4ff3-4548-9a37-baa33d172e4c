{"version": 3, "sources": ["../../src/client/remove-base-path.ts"], "sourcesContent": ["import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n"], "names": ["has<PERSON>ase<PERSON><PERSON>", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "removeBasePath", "path", "__NEXT_MANUAL_CLIENT_BASE_PATH", "length", "slice", "startsWith"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAiB;AAE7C,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAEnE,OAAO,SAASC,eAAeC,IAAY;IACzC,IAAIJ,QAAQC,GAAG,CAACI,8BAA8B,EAAE;QAC9C,IAAI,CAACP,YAAYM,OAAO;YACtB,OAAOA;QACT;IACF;IAEA,iDAAiD;IACjD,IAAIL,SAASO,MAAM,KAAK,GAAG,OAAOF;IAElCA,OAAOA,KAAKG,KAAK,CAACR,SAASO,MAAM;IACjC,IAAI,CAACF,KAAKI,UAAU,CAAC,MAAMJ,OAAO,AAAC,MAAGA;IACtC,OAAOA;AACT"}