{"version": 3, "sources": ["../../../../src/lib/metadata/types/metadata-types.ts"], "sourcesContent": ["/**\n *\n * Metadata types\n *\n */\n\nexport interface DeprecatedMetadataFields {\n  /**\n   * Deprecated options that have a preferred method\n   * @deprecated Use appWebApp to configure mobile-web-app-capable which provides\n   */\n  'apple-touch-fullscreen'?: never | undefined\n\n  /**\n   * Obsolete since iOS 7.\n   * @see https://web.dev/apple-touch-icon/\n   * @deprecated use icons.apple or instead\n   */\n  'apple-touch-icon-precomposed'?: never | undefined\n}\n\nexport type TemplateString =\n  | DefaultTemplateString\n  | AbsoluteTemplateString\n  | AbsoluteString\nexport type DefaultTemplateString = {\n  default: string\n  template: string\n}\nexport type AbsoluteTemplateString = {\n  absolute: string\n  template: string | null\n}\nexport type AbsoluteString = {\n  absolute: string\n}\n\nexport type Author = {\n  // renders as <link rel=\"author\"...\n  url?: string | URL | undefined\n  // renders as <meta name=\"author\"...\n  name?: string | undefined\n}\n\n// does not include \"unsafe-URL\". to use this users should\n// use '\"unsafe-URL\" as ReferrerEnum'\nexport type ReferrerEnum =\n  | 'no-referrer'\n  | 'origin'\n  | 'no-referrer-when-downgrade'\n  | 'origin-when-cross-origin'\n  | 'same-origin'\n  | 'strict-origin'\n  | 'strict-origin-when-cross-origin'\n\nexport type ColorSchemeEnum =\n  | 'normal'\n  | 'light'\n  | 'dark'\n  | 'light dark'\n  | 'dark light'\n  | 'only light'\n\ntype RobotsInfo = {\n  // all and none will be inferred from index/follow boolean options\n  index?: boolean | undefined\n  follow?: boolean | undefined\n\n  /** @deprecated set index to false instead */\n  noindex?: never | undefined\n  /** @deprecated set follow to false instead */\n  nofollow?: never | undefined\n\n  noarchive?: boolean | undefined\n  nosnippet?: boolean | undefined\n  noimageindex?: boolean | undefined\n  nocache?: boolean | undefined\n  notranslate?: boolean | undefined\n  indexifembedded?: boolean | undefined\n  nositelinkssearchbox?: boolean | undefined\n  unavailable_after?: string | undefined\n  'max-video-preview'?: number | string | undefined\n  'max-image-preview'?: 'none' | 'standard' | 'large' | undefined\n  'max-snippet'?: number | undefined\n}\nexport type Robots = RobotsInfo & {\n  // if you want to specify an alternate robots just for google\n  googleBot?: string | RobotsInfo | undefined\n}\n\nexport type ResolvedRobots = {\n  basic: string | null\n  googleBot: string | null\n}\n\nexport type IconURL = string | URL\nexport type Icon = IconURL | IconDescriptor\nexport type IconDescriptor = {\n  url: string | URL\n  type?: string | undefined\n  sizes?: string | undefined\n  color?: string | undefined\n  /** defaults to rel=\"icon\" unless superseded by Icons map */\n  rel?: string | undefined\n  media?: string | undefined\n  /**\n   * @see https://developer.mozilla.org/docs/Web/API/HTMLImageElement/fetchPriority\n   */\n  fetchPriority?: 'high' | 'low' | 'auto' | undefined\n}\n\nexport type Icons = {\n  /** rel=\"icon\" */\n  icon?: Icon | Icon[] | undefined\n  /** rel=\"shortcut icon\" */\n  shortcut?: Icon | Icon[] | undefined\n  /**\n   * @see https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/ConfiguringWebApplications/ConfiguringWebApplications.html\n   * rel=\"apple-touch-icon\"\n   */\n  apple?: Icon | Icon[] | undefined\n  /** rel inferred from descriptor, defaults to \"icon\" */\n  other?: IconDescriptor | IconDescriptor[] | undefined\n}\n\nexport type Verification = {\n  google?: null | string | number | (string | number)[] | undefined\n  yahoo?: null | string | number | (string | number)[] | undefined\n  yandex?: null | string | number | (string | number)[] | undefined\n  me?: null | string | number | (string | number)[] | undefined\n  // if you ad-hoc additional verification\n  other?:\n    | {\n        [name: string]: string | number | (string | number)[]\n      }\n    | undefined\n}\n\nexport type ResolvedVerification = {\n  google?: null | (string | number)[] | undefined\n  yahoo?: null | (string | number)[] | undefined\n  yandex?: null | (string | number)[] | undefined\n  me?: null | (string | number)[] | undefined\n  other?:\n    | {\n        [name: string]: (string | number)[]\n      }\n    | undefined\n}\n\nexport type ResolvedIcons = {\n  icon: IconDescriptor[]\n  apple: IconDescriptor[]\n  shortcut?: IconDescriptor[] | undefined\n  other?: IconDescriptor[] | undefined\n}\n\nexport type ThemeColorDescriptor = {\n  color: string\n  media?: string | undefined\n}\n\nexport type Restriction = {\n  relationship: 'allow' | 'deny'\n  content: string\n}\n\nexport type Videos = {\n  title: string\n  thumbnail_loc: string\n  description: string\n  content_loc?: string | undefined\n  player_loc?: string | undefined\n  duration?: number | undefined\n  expiration_date?: Date | string | undefined\n  rating?: number | undefined\n  view_count?: number | undefined\n  publication_date?: Date | string | undefined\n  family_friendly?: 'yes' | 'no' | undefined\n  restriction?: Restriction | undefined\n  platform?: Restriction | undefined\n  requires_subscription?: 'yes' | 'no' | undefined\n  uploader?:\n    | {\n        info?: string | undefined\n        content?: string | undefined\n      }\n    | undefined\n  live?: 'yes' | 'no' | undefined\n  tag?: string | undefined\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAmKD,WAuBC"}