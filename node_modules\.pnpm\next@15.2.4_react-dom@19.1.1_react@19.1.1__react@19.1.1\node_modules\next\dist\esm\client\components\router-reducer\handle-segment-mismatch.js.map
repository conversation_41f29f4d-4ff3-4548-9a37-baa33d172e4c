{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-segment-mismatch.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../server/app-render/types'\nimport { handleExternalUrl } from './reducers/navigate-reducer'\nimport type {\n  ReadonlyReducerState,\n  ReducerActions,\n} from './router-reducer-types'\n\n/**\n * Handles the case where the client router attempted to patch the tree but, due to a mismatch, the patch failed.\n * This will perform an MPA navigation to return the router to a valid state.\n */\nexport function handleSegmentMismatch(\n  state: ReadonlyReducerState,\n  action: ReducerActions,\n  treePatch: FlightRouterState\n) {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      'Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' +\n        'Reason: Segment mismatch\\n' +\n        `Last Action: ${action.type}\\n\\n` +\n        `Current Tree: ${JSON.stringify(state.tree)}\\n\\n` +\n        `Tree Patch Payload: ${JSON.stringify(treePatch)}`\n    )\n  }\n\n  return handleExternalUrl(state, {}, state.canonicalUrl, true)\n}\n"], "names": ["handleExternalUrl", "handleSegmentMismatch", "state", "action", "treePatch", "process", "env", "NODE_ENV", "console", "warn", "type", "JSON", "stringify", "tree", "canonicalUrl"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,8BAA6B;AAM/D;;;CAGC,GACD,OAAO,SAASC,sBACdC,KAA2B,EAC3BC,MAAsB,EACtBC,SAA4B;IAE5B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CC,QAAQC,IAAI,CACV,sJACE,+BACA,CAAA,AAAC,kBAAeN,OAAOO,IAAI,GAAC,MAAI,IAChC,CAAA,AAAC,mBAAgBC,KAAKC,SAAS,CAACV,MAAMW,IAAI,IAAE,MAAI,IAChD,CAAA,AAAC,yBAAsBF,KAAKC,SAAS,CAACR,UAAW;IAEvD;IAEA,OAAOJ,kBAAkBE,OAAO,CAAC,GAAGA,MAAMY,YAAY,EAAE;AAC1D"}