@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Enhanced text shadows for depth */
  h1, h2 {
    text-shadow: 0px 4px 12px rgba(0, 0, 0, 0.6);
  }
  
  h3 {
    text-shadow: 0px 2px 8px rgba(0, 0, 0, 0.4);
  }
}

/* Ultra-Premium Glass Effects - Much More Blurry & Cleaner */
.glass-ultra {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(40px) saturate(180%) brightness(110%);
  -webkit-backdrop-filter: blur(40px) saturate(180%) brightness(110%);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Dropdown Glass - Better Visibility for Desktop Dropdowns */
.dropdown-glass {
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(32px) saturate(180%) brightness(110%);
  -webkit-backdrop-filter: blur(32px) saturate(180%) brightness(110%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.8),
    0 8px 25px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
}

/* Mobile Dropdown - Solid Background for Better Readability */
.mobile-dropdown {
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.8),
    0 8px 30px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.shadow-glass {
  box-shadow: 
    0 16px 50px rgba(0, 0, 0, 0.7), 
    0 8px 25px rgba(0, 0, 0, 0.5), 
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
}

/* Enhanced Gradient System */
.enhanced-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 10s ease infinite;
}

.enhanced-gradient-bg {
  background-size: 400% 400%;
  animation: gradient-shift 10s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 50% 100%;
  }
  75% {
    background-position: 50% 0%;
  }
}

/* Premium Button Styles - Ultra Enhanced */
.premium-button {
  background: linear-gradient(135deg, #780206 0%, #780206 50%, #061161 100%);
  color: white;
  border: none;
  box-shadow: 
    0 8px 32px rgba(120, 2, 6, 0.6), 
    0 4px 16px rgba(6, 17, 97, 0.5), 
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 44px;
}

.premium-button:hover {
  background: linear-gradient(135deg, #8a0308 0%, #8a0308 50%, #0a1470 100%);
  box-shadow: 
    0 12px 48px rgba(120, 2, 6, 0.7), 
    0 6px 24px rgba(6, 17, 97, 0.6), 
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) scale(1.02);
}

.hero-button {
  background: linear-gradient(135deg, #780206 0%, #780206 30%, #ec4899 60%, #061161 100%);
  color: white;
  border: none;
  padding: 1.25rem 2.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  border-radius: 1.25rem;
  box-shadow: 
    0 12px 48px rgba(120, 2, 6, 0.6), 
    0 6px 24px rgba(236, 72, 153, 0.5), 
    0 3px 12px rgba(6, 17, 97, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 56px;
}

.hero-button:hover {
  background: linear-gradient(135deg, #8a0308 0%, #8a0308 30%, #f472b6 60%, #0a1470 100%);
  box-shadow: 
    0 16px 64px rgba(120, 2, 6, 0.7), 
    0 8px 32px rgba(236, 72, 153, 0.6), 
    0 4px 16px rgba(6, 17, 97, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.35);
  transform: translateY(-4px) scale(1.03);
}

.secondary-button {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(32px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.25);
  padding: 1.25rem 2.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  border-radius: 1.25rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.5), 
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 56px;
}

.secondary-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.6), 
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-3px) scale(1.02);
}

.glass-button {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(32px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  min-height: 44px;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

/* Grid Patterns */
.bg-grid-white\/\[0\.02\] {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.025) 1px, transparent 1px);
}

.bg-grid-16 {
  background-size: 16px 16px;
}

.bg-grid-32 {
  background-size: 32px 32px;
}

/* Gradient Utilities */
.bg-gradient-radial {
  background: radial-gradient(var(--tw-gradient-stops));
}

.bg-gradient-conic {
  background: conic-gradient(var(--tw-gradient-stops));
}

/* Mobile-First Responsive Design */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .hero-button, .secondary-button {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 48px;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }
  .group:hover .group-hover\:scale-110 {
    transform: none;
  }
  .group:hover .group-hover\:translate-x-1 {
    transform: none;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

@media (min-width: 768px) {
  ::-webkit-scrollbar {
    width: 10px;
  }
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #780206, #ec4899, #061161);
  border-radius: 5px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #8a0308, #f472b6, #0a1470);
}

/* Selection */
::selection {
  background: rgba(120, 2, 6, 0.4);
  color: white;
}

/* Enhanced Focus States */
*:focus-visible {
  outline: 3px solid rgba(120, 2, 6, 0.7);
  outline-offset: 3px;
  border-radius: 4px;
}

/* Spacing Utilities */
.w-18 {
  width: 4.5rem;
}

.h-18 {
  height: 4.5rem;
}

/* Safe Area Support for Mobile */
@supports (padding: max(0px)) {
  .safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  .safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 768px) {
  input[type="email"],
  input[type="text"],
  input[type="password"],
  textarea {
    font-size: 16px;
  }
}

/* Loading Animation */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
