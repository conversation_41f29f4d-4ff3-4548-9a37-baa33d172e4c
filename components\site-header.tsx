"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpRight, Menu, X, ChevronDown } from 'lucide-react'
import Link from "next/link"
import { useState, useEffect } from "react"

// Navigation configuration
const navigationConfig = [
  {
    name: "Projects",
    role: "navigation",
    href: "",
    hasDropdown: true,
    dropdownItems: [
      { name: "Blockman Legacy", href: "/p/blockman-legacy", role: "link" },
      { name: "CyltraDash (Coming Soon)", href: "#", role: "link" },
    ]
  },
  {
    name: "Link<PERSON>",
    role: "navigation",
    href: "",
    hasDropdown: true,
    dropdownItems: [
      { name: "Blog", href: "https://blog.cythro.com", role: "link" },
      { name: "Status", href: "https://status.cythro.com", role: "link" },
      { name: "Branding", href: "/branding", role: "link" },
    ]
  },
  {
    name: "Contact",
    role: "navigation",
    href: "",
    hasDropdown: true,
    dropdownItems: [
      { name: "Discord", href: "#", role: "link" },
      { name: "Email", href: "mailto:<EMAIL>", role: "link" },
    ]
  }
]

export default function SiteHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>({})
  const [hoverTimeouts, setHoverTimeouts] = useState<Record<string, NodeJS.Timeout>>({})

  // Handle mouse enter for dropdown
  const handleMouseEnter = (dropdownName: string) => {
    if (hoverTimeouts[dropdownName]) {
      clearTimeout(hoverTimeouts[dropdownName])
      setHoverTimeouts(prev => {
        const newTimeouts = { ...prev }
        delete newTimeouts[dropdownName]
        return newTimeouts
      })
    }
    setOpenDropdowns(prev => ({ ...prev, [dropdownName]: true }))
  }

  // Handle mouse leave for dropdown
  const handleMouseLeave = (dropdownName: string) => {
    const timeout = setTimeout(() => {
      setOpenDropdowns(prev => ({ ...prev, [dropdownName]: false }))
    }, 150) // Small delay to prevent flickering
    setHoverTimeouts(prev => ({ ...prev, [dropdownName]: timeout }))
  }

  // Toggle dropdown for mobile/click
  const toggleDropdown = (dropdownName: string) => {
    setOpenDropdowns(prev => ({ ...prev, [dropdownName]: !prev[dropdownName] }))
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-dropdown]')) {
        setOpenDropdowns({})
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
      Object.values(hoverTimeouts).forEach(timeout => clearTimeout(timeout))
    }
  }, [hoverTimeouts])

  return (
    <header className="fixed top-4 md:top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-7xl px-3 md:px-4">
      <nav className="glass-ultra rounded-2xl md:rounded-3xl px-4 md:px-8 py-3 md:py-4 border border-white/20 shadow-glass">
        <div className="flex items-center justify-between w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <div className="relative">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-xl md:rounded-2xl flex items-center justify-center">
                <img src="/cythro.png" alt="Cythro Logo" className="w-full h-full object-contain" />
              </div>
            </div>
            <div className="hidden sm:block">
              <span className="text-lg md:text-xl lg:text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent">
                Cythro
              </span>
              <p className="text-gray-400 text-xs font-medium -mt-1 hidden md:block">Digital Creators</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationConfig.map((item) => (
              <div key={item.name} className="relative">
                {item.hasDropdown ? (
                  <div
                    className="relative"
                    data-dropdown
                    onMouseEnter={() => handleMouseEnter(item.name)}
                    onMouseLeave={() => handleMouseLeave(item.name)}
                  >
                    <button
                      role={item.role}
                      onClick={() => toggleDropdown(item.name)}
                      className="text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group flex items-center space-x-1"
                    >
                      <span>{item.name}</span>
                      <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${openDropdowns[item.name] ? 'rotate-180' : ''}`} />
                      <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300"></div>
                    </button>

                    {/* Dropdown Menu */}
                    {openDropdowns[item.name] && (
                      <div className="absolute top-full left-0 mt-2 w-56 dropdown-glass rounded-2xl border border-white/20 shadow-glass overflow-hidden z-50">
                        {item.dropdownItems?.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            role={dropdownItem.role}
                            className="block px-5 py-3 text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-200 border-b border-white/10 last:border-b-0 font-medium"
                            onClick={() => setOpenDropdowns(prev => ({ ...prev, [item.name]: false }))}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    role={item.role}
                    className="text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group"
                  >
                    {item.name}
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300"></div>
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <Button
              variant="ghost"
              size="sm"
              className="hidden xl:inline-flex text-gray-300 hover:text-white hover:bg-white/10 glass-button text-sm"
            >
              Say Hello
            </Button>
            <Button size="sm" className="premium-button hidden md:inline-flex text-sm">
              <span className="hidden lg:inline">Let's Work Together</span>
              <span className="lg:hidden">Work Together</span>
              <ArrowUpRight className="ml-1 lg:ml-2 w-4 h-4" />
            </Button>
            <Button size="sm" className="premium-button md:hidden text-xs px-3">
              Work
              <ArrowUpRight className="ml-1 w-3 h-3" />
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-gray-300 hover:text-white p-2 ml-2"
              onClick={() => {
                setMobileMenuOpen(!mobileMenuOpen)
                if (!mobileMenuOpen) setOpenDropdowns({})
              }}
            >
              {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu - Improved */}
        {mobileMenuOpen && (
          <div className="lg:hidden mt-6 pt-6 border-t border-white/20">
            <div className="mobile-dropdown rounded-2xl p-6 -mx-4">
              <div className="flex flex-col space-y-4">
                {navigationConfig.map((item) => (
                  <div key={item.name}>
                    {item.hasDropdown ? (
                      <div>
                        <button
                          role={item.role}
                          onClick={() => toggleDropdown(item.name)}
                          className="w-full text-left text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-3 border-b border-white/10 flex items-center justify-between"
                        >
                          <span>{item.name}</span>
                          <ChevronDown className={`w-5 h-5 transition-transform duration-200 ${openDropdowns[item.name] ? 'rotate-180' : ''}`} />
                        </button>
                        {openDropdowns[item.name] && (
                          <div className="mt-3 ml-4 space-y-1">
                            {item.dropdownItems?.map((dropdownItem) => (
                              <Link
                                key={dropdownItem.name}
                                href={dropdownItem.href}
                                role={dropdownItem.role}
                                className="block text-gray-300 hover:text-white transition-colors duration-300 font-medium py-3 pl-4 border-l-2 border-white/20 hover:border-white/40 rounded-r-lg hover:bg-white/5"
                                onClick={() => {
                                  setMobileMenuOpen(false)
                                  setOpenDropdowns({})
                                }}
                              >
                                {dropdownItem.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        role={item.role}
                        className="text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-3 border-b border-white/10 last:border-b-0 block"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
                <div className="pt-6 border-t border-white/10 space-y-3">
                  <Button
                    size="lg"
                    className="w-full premium-button"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Let's Work Together
                    <ArrowUpRight className="ml-2 w-5 h-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="w-full text-white hover:text-gray-300 hover:bg-white/10 glass-button"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Say Hello
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
