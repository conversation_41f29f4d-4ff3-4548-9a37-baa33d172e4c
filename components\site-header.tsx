"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpRight, Code, Menu, X, ChevronDown } from 'lucide-react'
import Link from "next/link"
import { useState, useEffect } from "react"

// Navigation configuration
const navigationConfig = [
  {
    name: "Projects",
    role: "navigation",
    href: "#projects",
    hasDropdown: true,
    dropdownItems: [
      { name: "Web Development", href: "/projects/web-development", role: "link" },
      { name: "Mobile Apps", href: "/projects/mobile-apps", role: "link" },
      { name: "UI/UX Design", href: "/projects/ui-ux-design", role: "link" },
      { name: "Branding", href: "/projects/branding", role: "link" },
    ]
  },
  {
    name: "Services",
    role: "link",
    href: "#services",
    hasDropdown: false
  },
  {
    name: "About",
    role: "link",
    href: "#about",
    hasDropdown: false
  },
  {
    name: "Contact",
    role: "link",
    href: "#contact",
    hasDropdown: false
  }
]

export default function SiteHeader() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [projectsDropdownOpen, setProjectsDropdownOpen] = useState(false)
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)

  // Handle mouse enter for dropdown
  const handleMouseEnter = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
    setProjectsDropdownOpen(true)
  }

  // Handle mouse leave for dropdown
  const handleMouseLeave = () => {
    const timeout = setTimeout(() => {
      setProjectsDropdownOpen(false)
    }, 150) // Small delay to prevent flickering
    setHoverTimeout(timeout)
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout)
      }
    }
  }, [hoverTimeout])

  return (
    <header className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-7xl px-4">
      <nav className="glass-ultra rounded-3xl px-8 py-4 border border-white/20 shadow-glass">
        <div className="flex items-center justify-between w-full">
          {/* Logo */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <div className="relative">
              <div className="w-14 h-14 md:w-16 md:h-16 rounded-xl md:rounded-2xl flex items-center justify-center">
                <img src="/cythro.png" alt="Cythro Logo" className="w-full h-full object-contain" />
              </div>
            </div>
            <div>
              <span className="text-xl md:text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent">
                Cythro
              </span>
              <p className="text-gray-400 text-xs font-medium -mt-1 hidden sm:block">Digital Creators</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationConfig.map((item) => (
              <div key={item.name} className="relative">
                {item.hasDropdown ? (
                  <div
                    className="relative"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                  >
                    <button
                      role={item.role}
                      onClick={() => setProjectsDropdownOpen(!projectsDropdownOpen)}
                      className="text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group flex items-center space-x-1"
                    >
                      <span>{item.name}</span>
                      <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${projectsDropdownOpen ? 'rotate-180' : ''}`} />
                      <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300"></div>
                    </button>

                    {/* Dropdown Menu */}
                    {projectsDropdownOpen && (
                      <div className="absolute top-full left-0 mt-2 w-48 dropdown-glass rounded-2xl border border-white/20 shadow-glass overflow-hidden z-50">
                        {item.dropdownItems?.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            role={dropdownItem.role}
                            className="block px-4 py-3 text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                            onClick={() => setProjectsDropdownOpen(false)}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    role={item.role}
                    className="text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group"
                  >
                    {item.name}
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300"></div>
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <Button
              variant="ghost"
              size="sm"
              className="hidden md:inline-flex text-gray-300 hover:text-white hover:bg-white/10 glass-button"
            >
              Say Hello
            </Button>
            <Button size="sm" className="premium-button hidden sm:inline-flex">
              Let's Work Together
              <ArrowUpRight className="ml-2 w-4 h-4" />
            </Button>
            <Button size="sm" className="premium-button sm:hidden">
              Work Together
              <ArrowUpRight className="ml-1 w-4 h-4" />
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-gray-300 hover:text-white p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu - Improved */}
        {mobileMenuOpen && (
          <div className="lg:hidden mt-6 pt-6 border-t border-white/20">
            <div className="mobile-dropdown rounded-2xl p-6 -mx-4">
              <div className="flex flex-col space-y-6">
                {navigationConfig.map((item) => (
                  <div key={item.name}>
                    {item.hasDropdown ? (
                      <div>
                        <button
                          role={item.role}
                          onClick={() => setProjectsDropdownOpen(!projectsDropdownOpen)}
                          className="w-full text-left text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 flex items-center justify-between"
                        >
                          <span>{item.name}</span>
                          <ChevronDown className={`w-5 h-5 transition-transform duration-200 ${projectsDropdownOpen ? 'rotate-180' : ''}`} />
                        </button>
                        {projectsDropdownOpen && (
                          <div className="mt-2 ml-4 space-y-2">
                            {item.dropdownItems?.map((dropdownItem) => (
                              <Link
                                key={dropdownItem.name}
                                href={dropdownItem.href}
                                role={dropdownItem.role}
                                className="block text-gray-300 hover:text-white transition-colors duration-300 font-medium py-2 pl-4 border-l-2 border-white/20"
                                onClick={() => {
                                  setMobileMenuOpen(false)
                                  setProjectsDropdownOpen(false)
                                }}
                              >
                                {dropdownItem.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        role={item.role}
                        className="text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 last:border-b-0 block"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
                <div className="pt-4 border-t border-white/10">
                  <Button
                    size="lg"
                    className="w-full premium-button mb-3"
                  >
                    Let's Work Together
                    <ArrowUpRight className="ml-2 w-5 h-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="w-full text-white hover:text-gray-300 hover:bg-white/10 glass-button"
                  >
                    Say Hello
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
