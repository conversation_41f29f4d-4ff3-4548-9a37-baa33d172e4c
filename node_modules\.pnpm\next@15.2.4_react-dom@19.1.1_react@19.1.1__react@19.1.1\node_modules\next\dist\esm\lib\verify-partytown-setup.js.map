{"version": 3, "sources": ["../../src/lib/verify-partytown-setup.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { bold, cyan, red } from './picocolors'\n\nimport path from 'path'\nimport { hasNecessaryDependencies } from './has-necessary-dependencies'\nimport type { NecessaryDependencies } from './has-necessary-dependencies'\nimport { fileExists, FileType } from './file-exists'\nimport { FatalError } from './fatal-error'\nimport * as Log from '../build/output/log'\nimport { getPkgManager } from './helpers/get-pkg-manager'\n\nasync function missingDependencyError(dir: string) {\n  const packageManager = getPkgManager(dir)\n\n  throw new FatalError(\n    bold(\n      red(\n        \"It looks like you're trying to use Partytown with next/script but do not have the required package(s) installed.\"\n      )\n    ) +\n      '\\n\\n' +\n      bold(`Please install Partytown by running:`) +\n      '\\n\\n' +\n      `\\t${bold(\n        cyan(\n          (packageManager === 'yarn'\n            ? 'yarn add --dev'\n            : packageManager === 'pnpm'\n              ? 'pnpm install --save-dev'\n              : 'npm install --save-dev') + ' @builder.io/partytown'\n        )\n      )}` +\n      '\\n\\n' +\n      bold(\n        `If you are not trying to use Partytown, please disable the experimental ${cyan(\n          '\"nextScriptWorkers\"'\n        )} flag in next.config.js.`\n      ) +\n      '\\n'\n  )\n}\n\nasync function copyPartytownStaticFiles(\n  deps: NecessaryDependencies,\n  staticDir: string\n) {\n  const partytownLibDir = path.join(staticDir, '~partytown')\n  const hasPartytownLibDir = await fileExists(\n    partytownLibDir,\n    FileType.Directory\n  )\n\n  if (hasPartytownLibDir) {\n    await promises.rm(partytownLibDir, { recursive: true, force: true })\n  }\n\n  const { copyLibFiles } = await Promise.resolve(\n    require(path.join(deps.resolved.get('@builder.io/partytown')!, '../utils'))\n  )\n\n  await copyLibFiles(partytownLibDir)\n}\n\nexport async function verifyPartytownSetup(\n  dir: string,\n  targetDir: string\n): Promise<void> {\n  try {\n    const partytownDeps: NecessaryDependencies = await hasNecessaryDependencies(\n      dir,\n      [\n        {\n          file: '@builder.io/partytown',\n          pkg: '@builder.io/partytown',\n          exportsRestrict: false,\n        },\n      ]\n    )\n\n    if (partytownDeps.missing?.length > 0) {\n      await missingDependencyError(dir)\n    } else {\n      try {\n        await copyPartytownStaticFiles(partytownDeps, targetDir)\n      } catch (err) {\n        Log.warn(\n          `Partytown library files could not be copied to the static directory. Please ensure that ${bold(\n            cyan('@builder.io/partytown')\n          )} is installed as a dependency.`\n        )\n      }\n    }\n  } catch (err) {\n    // Don't show a stack trace when there is an error due to missing dependencies\n    if (err instanceof FatalError) {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": ["promises", "bold", "cyan", "red", "path", "hasNecessaryDependencies", "fileExists", "FileType", "FatalE<PERSON>r", "Log", "getPkgManager", "missingDependencyError", "dir", "packageManager", "copyPartytownStaticFiles", "deps", "staticDir", "partytownLibDir", "join", "hasPartytownLibDir", "Directory", "rm", "recursive", "force", "copyLibFiles", "Promise", "resolve", "require", "resolved", "get", "verifyPartytownSetup", "targetDir", "partytownDeps", "file", "pkg", "exportsRestrict", "missing", "length", "err", "warn", "console", "error", "message", "process", "exit"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAc;AAE9C,OAAOC,UAAU,OAAM;AACvB,SAASC,wBAAwB,QAAQ,+BAA8B;AAEvE,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAe;AACpD,SAASC,UAAU,QAAQ,gBAAe;AAC1C,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,aAAa,QAAQ,4BAA2B;AAEzD,eAAeC,uBAAuBC,GAAW;IAC/C,MAAMC,iBAAiBH,cAAcE;IAErC,MAAM,qBAyBL,CAzBK,IAAIJ,WACRP,KACEE,IACE,uHAGF,SACAF,KAAK,CAAC,oCAAoC,CAAC,IAC3C,SACA,CAAC,EAAE,EAAEA,KACHC,KACE,AAACW,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACjB,4BACA,wBAAuB,IAAK,4BAEnC,GACH,SACAZ,KACE,CAAC,wEAAwE,EAAEC,KACzE,uBACA,wBAAwB,CAAC,IAE7B,OAxBE,qBAAA;eAAA;oBAAA;sBAAA;IAyBN;AACF;AAEA,eAAeY,yBACbC,IAA2B,EAC3BC,SAAiB;IAEjB,MAAMC,kBAAkBb,KAAKc,IAAI,CAACF,WAAW;IAC7C,MAAMG,qBAAqB,MAAMb,WAC/BW,iBACAV,SAASa,SAAS;IAGpB,IAAID,oBAAoB;QACtB,MAAMnB,SAASqB,EAAE,CAACJ,iBAAiB;YAAEK,WAAW;YAAMC,OAAO;QAAK;IACpE;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,QAAQC,OAAO,CAC5CC,QAAQvB,KAAKc,IAAI,CAACH,KAAKa,QAAQ,CAACC,GAAG,CAAC,0BAA2B;IAGjE,MAAML,aAAaP;AACrB;AAEA,OAAO,eAAea,qBACpBlB,GAAW,EACXmB,SAAiB;IAEjB,IAAI;YAYEC;QAXJ,MAAMA,gBAAuC,MAAM3B,yBACjDO,KACA;YACE;gBACEqB,MAAM;gBACNC,KAAK;gBACLC,iBAAiB;YACnB;SACD;QAGH,IAAIH,EAAAA,yBAAAA,cAAcI,OAAO,qBAArBJ,uBAAuBK,MAAM,IAAG,GAAG;YACrC,MAAM1B,uBAAuBC;QAC/B,OAAO;YACL,IAAI;gBACF,MAAME,yBAAyBkB,eAAeD;YAChD,EAAE,OAAOO,KAAK;gBACZ7B,IAAI8B,IAAI,CACN,CAAC,wFAAwF,EAAEtC,KACzFC,KAAK,0BACL,8BAA8B,CAAC;YAErC;QACF;IACF,EAAE,OAAOoC,KAAK;QACZ,8EAA8E;QAC9E,IAAIA,eAAe9B,YAAY;YAC7BgC,QAAQC,KAAK,CAACH,IAAII,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMN;IACR;AACF"}