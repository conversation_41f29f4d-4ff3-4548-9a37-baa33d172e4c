{"version": 3, "sources": ["../../../src/client/components/default-layout.tsx"], "sourcesContent": ["import React from 'react'\n\nexport default function DefaultLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html>\n      <body>{children}</body>\n    </html>\n  )\n}\n"], "names": ["React", "DefaultLayout", "children", "html", "body"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAEzB,eAAe,SAASC,cAAc,KAIrC;IAJqC,IAAA,EACpCC,QAAQ,EAGT,GAJqC;IAKpC,qBACE,KAACC;kBACC,cAAA,KAACC;sBAAMF;;;AAGb"}