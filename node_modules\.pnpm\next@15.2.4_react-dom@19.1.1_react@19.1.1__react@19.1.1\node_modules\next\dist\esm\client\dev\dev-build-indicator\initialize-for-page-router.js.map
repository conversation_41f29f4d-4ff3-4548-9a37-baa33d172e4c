{"version": 3, "sources": ["../../../../src/client/dev/dev-build-indicator/initialize-for-page-router.ts"], "sourcesContent": ["import { addMessageListener } from '../../components/react-dev-overlay/pages/websocket'\nimport { devBuildIndicator } from './internal/dev-build-indicator'\nimport { handleDevBuildIndicatorHmrEvents } from './internal/handle-dev-build-indicator-hmr-events'\n\n/** Integrates the generic dev build indicator with the Pages Router. */\nexport const initializeDevBuildIndicatorForPageRouter = () => {\n  if (!process.env.__NEXT_DEV_INDICATOR) {\n    return\n  }\n\n  devBuildIndicator.initialize()\n\n  // Add message listener specifically for Pages Router to handle lifecycle events\n  // related to dev builds (building, built, sync)\n  addMessageListener(handleDevBuildIndicatorHmrEvents)\n}\n"], "names": ["addMessageListener", "devBuildIndicator", "handleDevBuildIndicatorHmrEvents", "initializeDevBuildIndicatorForPageRouter", "process", "env", "__NEXT_DEV_INDICATOR", "initialize"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,iBAAiB,QAAQ,iCAAgC;AAClE,SAASC,gCAAgC,QAAQ,mDAAkD;AAEnG,sEAAsE,GACtE,OAAO,MAAMC,2CAA2C;IACtD,IAAI,CAACC,QAAQC,GAAG,CAACC,oBAAoB,EAAE;QACrC;IACF;IAEAL,kBAAkBM,UAAU;IAE5B,gFAAgF;IAChF,gDAAgD;IAChDP,mBAAmBE;AACrB,EAAC"}