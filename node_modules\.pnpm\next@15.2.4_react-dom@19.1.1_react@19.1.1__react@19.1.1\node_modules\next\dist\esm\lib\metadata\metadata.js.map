{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "sourcesContent": ["import React, { Suspense, cache, cloneElement } from 'react'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { CreateServerParamsForMetadata } from '../../server/request/params'\nimport type { StreamingMetadataResolvedState } from '../../client/components/metadata/types'\nimport {\n  AppleWebAppMeta,\n  FormatDetectionMeta,\n  ItunesMeta,\n  BasicMeta,\n  ViewportMeta,\n  VerificationMeta,\n  FacebookMeta,\n} from './generate/basic'\nimport { AlternatesMetadata } from './generate/alternate'\nimport {\n  OpenGraphMetadata,\n  TwitterMetadata,\n  AppLinksMeta,\n} from './generate/opengraph'\nimport { IconsMetadata } from './generate/icons'\nimport {\n  type MetadataErrorType,\n  resolveMetadata,\n  resolveViewport,\n} from './resolve-metadata'\nimport { MetaFilter } from './generate/meta'\nimport type {\n  ResolvedMetadata,\n  ResolvedViewport,\n} from './types/metadata-interface'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport type { MetadataContext } from './types/resolvers'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n} from './metadata-constants'\nimport {\n  AsyncMetadata,\n  AsyncMetadataOutlet,\n} from '../../client/components/metadata/async-metadata'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\n\n// Use a promise to share the status of the metadata resolving,\n// returning two components `MetadataTree` and `MetadataOutlet`\n// `MetadataTree` is the one that will be rendered at first in the content sequence for metadata tags.\n// `MetadataOutlet` is the one that will be rendered under error boundaries for metadata resolving errors.\n// In this way we can let the metadata tags always render successfully,\n// and the error will be caught by the error boundary and trigger fallbacks.\nexport function createMetadataComponents({\n  tree,\n  searchParams,\n  metadataContext,\n  getDynamicParamFromSegment,\n  appUsingSizeAdjustment,\n  errorType,\n  createServerParamsForMetadata,\n  workStore,\n  MetadataBoundary,\n  ViewportBoundary,\n  serveStreamingMetadata,\n}: {\n  tree: LoaderTree\n  searchParams: Promise<ParsedUrlQuery>\n  metadataContext: MetadataContext\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  appUsingSizeAdjustment: boolean\n  errorType?: MetadataErrorType | 'redirect'\n  createServerParamsForMetadata: CreateServerParamsForMetadata\n  workStore: WorkStore\n  MetadataBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  ViewportBoundary: (props: { children: React.ReactNode }) => React.ReactNode\n  serveStreamingMetadata: boolean\n}): {\n  MetadataTree: React.ComponentType\n  ViewportTree: React.ComponentType\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  StreamingMetadataOutlet: React.ComponentType\n} {\n  function ViewportTree() {\n    return (\n      <>\n        <ViewportBoundary>\n          <Viewport />\n        </ViewportBoundary>\n        {/* This meta tag is for next/font which is still required to be blocking. */}\n        {appUsingSizeAdjustment ? (\n          <meta name=\"next-size-adjust\" content=\"\" />\n        ) : null}\n      </>\n    )\n  }\n\n  function MetadataTree() {\n    return (\n      <MetadataBoundary>\n        <Metadata />\n      </MetadataBoundary>\n    )\n  }\n\n  function viewport() {\n    return getResolvedViewport(\n      tree,\n      searchParams,\n      getDynamicParamFromSegment,\n      createServerParamsForMetadata,\n      workStore,\n      errorType\n    )\n  }\n\n  async function Viewport() {\n    try {\n      return await viewport()\n    } catch (error) {\n      if (!errorType && isHTTPAccessFallbackError(error)) {\n        try {\n          return await getNotFoundViewport(\n            tree,\n            searchParams,\n            getDynamicParamFromSegment,\n            createServerParamsForMetadata,\n            workStore\n          )\n        } catch {}\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return null\n    }\n  }\n  Viewport.displayName = VIEWPORT_BOUNDARY_NAME\n\n  function metadata() {\n    return getResolvedMetadata(\n      tree,\n      searchParams,\n      getDynamicParamFromSegment,\n      metadataContext,\n      createServerParamsForMetadata,\n      workStore,\n      errorType\n    )\n  }\n\n  async function resolveFinalMetadata(): Promise<StreamingMetadataResolvedState> {\n    let result: React.ReactNode\n    let error = null\n    try {\n      result = await metadata()\n      return {\n        metadata: result,\n        error: null,\n        digest: undefined,\n      }\n    } catch (metadataErr) {\n      error = metadataErr\n      if (!errorType && isHTTPAccessFallbackError(metadataErr)) {\n        try {\n          result = await getNotFoundMetadata(\n            tree,\n            searchParams,\n            getDynamicParamFromSegment,\n            metadataContext,\n            createServerParamsForMetadata,\n            workStore\n          )\n          return {\n            metadata: result,\n            error,\n            digest: (error as any)?.digest,\n          }\n        } catch (notFoundMetadataErr) {\n          error = notFoundMetadataErr\n          // In PPR rendering we still need to throw the postpone error.\n          // If metadata is postponed, React needs to be aware of the location of error.\n          if (serveStreamingMetadata && isPostpone(notFoundMetadataErr)) {\n            throw notFoundMetadataErr\n          }\n        }\n      }\n      // In PPR rendering we still need to throw the postpone error.\n      // If metadata is postponed, React needs to be aware of the location of error.\n      if (serveStreamingMetadata && isPostpone(metadataErr)) {\n        throw metadataErr\n      }\n      // We don't actually want to error in this component. We will\n      // also error in the MetadataOutlet which causes the error to\n      // bubble from the right position in the page to be caught by the\n      // appropriate boundaries\n      return {\n        metadata: result,\n        error,\n        digest: (error as any)?.digest,\n      }\n    }\n  }\n  async function Metadata() {\n    const promise = resolveFinalMetadata()\n    if (serveStreamingMetadata) {\n      return (\n        <Suspense fallback={null}>\n          <AsyncMetadata promise={promise} />\n        </Suspense>\n      )\n    }\n    const metadataState = await promise\n    return metadataState.metadata\n  }\n\n  Metadata.displayName = METADATA_BOUNDARY_NAME\n\n  async function getMetadataReady(): Promise<void> {\n    // Only warm up metadata() call when it's blocking metadata,\n    // otherwise it will be fully managed by AsyncMetadata component.\n    if (!serveStreamingMetadata) {\n      await metadata()\n    }\n    return undefined\n  }\n\n  async function getViewportReady(): Promise<void> {\n    await viewport()\n    return undefined\n  }\n\n  function StreamingMetadataOutlet() {\n    if (serveStreamingMetadata) {\n      return <AsyncMetadataOutlet promise={resolveFinalMetadata()} />\n    }\n    return null\n  }\n\n  return {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  }\n}\n\nconst getResolvedMetadata = cache(getResolvedMetadataImpl)\nasync function getResolvedMetadataImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderMetadata(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    createServerParamsForMetadata,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundMetadata = cache(getNotFoundMetadataImpl)\nasync function getNotFoundMetadataImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderMetadata(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    metadataContext,\n    createServerParamsForMetadata,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nconst getResolvedViewport = cache(getResolvedViewportImpl)\nasync function getResolvedViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore,\n  errorType?: MetadataErrorType | 'redirect'\n): Promise<React.ReactNode> {\n  const errorConvention = errorType === 'redirect' ? undefined : errorType\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore,\n    errorConvention\n  )\n}\n\nconst getNotFoundViewport = cache(getNotFoundViewportImpl)\nasync function getNotFoundViewportImpl(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n): Promise<React.ReactNode> {\n  const notFoundErrorConvention = 'not-found'\n  return renderViewport(\n    tree,\n    searchParams,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore,\n    notFoundErrorConvention\n  )\n}\n\nasync function renderMetadata(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  metadataContext: MetadataContext,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const resolvedMetadata = await resolveMetadata(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore,\n    metadataContext\n  )\n  const elements: Array<React.ReactNode> =\n    createMetadataElements(resolvedMetadata)\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nasync function renderViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore,\n  errorConvention?: MetadataErrorType\n) {\n  const notFoundResolvedViewport = await resolveViewport(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore\n  )\n\n  const elements: Array<React.ReactNode> = createViewportElements(\n    notFoundResolvedViewport\n  )\n  return (\n    <>\n      {elements.map((el, index) => {\n        return cloneElement(el as React.ReactElement, { key: index })\n      })}\n    </>\n  )\n}\n\nfunction createMetadataElements(metadata: ResolvedMetadata) {\n  return MetaFilter([\n    BasicMeta({ metadata }),\n    AlternatesMetadata({ alternates: metadata.alternates }),\n    ItunesMeta({ itunes: metadata.itunes }),\n    FacebookMeta({ facebook: metadata.facebook }),\n    FormatDetectionMeta({ formatDetection: metadata.formatDetection }),\n    VerificationMeta({ verification: metadata.verification }),\n    AppleWebAppMeta({ appleWebApp: metadata.appleWebApp }),\n    OpenGraphMetadata({ openGraph: metadata.openGraph }),\n    TwitterMetadata({ twitter: metadata.twitter }),\n    AppLinksMeta({ appLinks: metadata.appLinks }),\n    IconsMetadata({ icons: metadata.icons }),\n  ])\n}\n\nfunction createViewportElements(viewport: ResolvedViewport) {\n  return MetaFilter([ViewportMeta({ viewport: viewport })])\n}\n"], "names": ["React", "Suspense", "cache", "cloneElement", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMeta", "ViewportMeta", "VerificationMeta", "FacebookMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "resolveViewport", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isHTTPAccessFallbackError", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "AsyncMetadata", "AsyncMetadataOutlet", "isPostpone", "createMetadataComponents", "tree", "searchParams", "metadataContext", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "createServerParamsForMetadata", "workStore", "MetadataBoundary", "ViewportBoundary", "serveStreamingMetadata", "ViewportTree", "Viewport", "meta", "name", "content", "MetadataTree", "<PERSON><PERSON><PERSON>", "viewport", "getResolvedViewport", "error", "getNotFoundViewport", "displayName", "metadata", "getResolvedMetadata", "resolveFinalMetadata", "result", "digest", "undefined", "metadataErr", "getNotFoundMetadata", "notFoundMetadataErr", "promise", "fallback", "metadataState", "getMetadataReady", "getViewportReady", "StreamingMetadataOutlet", "getResolvedMetadataImpl", "errorConvention", "renderMetadata", "getNotFoundMetadataImpl", "notFoundErrorConvention", "getResolvedViewportImpl", "renderViewport", "getNotFoundViewportImpl", "resolvedMetadata", "elements", "createMetadataElements", "map", "el", "index", "key", "notFoundResolvedViewport", "createViewportElements", "alternates", "itunes", "facebook", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons"], "mappings": ";AAAA,OAAOA,SAASC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,QAAQ,QAAO;AAM5D,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAEEC,eAAe,EACfC,eAAe,QACV,qBAAoB;AAC3B,SAASC,UAAU,QAAQ,kBAAiB;AAK5C,SAASC,yBAAyB,QAAQ,oEAAmE;AAG7G,SACEC,sBAAsB,EACtBC,sBAAsB,QACjB,uBAAsB;AAC7B,SACEC,aAAa,EACbC,mBAAmB,QACd,kDAAiD;AACxD,SAASC,UAAU,QAAQ,4CAA2C;AAEtE,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,YAAY,EACZC,eAAe,EACfC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EACTC,6BAA6B,EAC7BC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,sBAAsB,EAavB;IAOC,SAASC;QACP,qBACE;;8BACE,KAACF;8BACC,cAAA,KAACG;;gBAGFR,uCACC,KAACS;oBAAKC,MAAK;oBAAmBC,SAAQ;qBACpC;;;IAGV;IAEA,SAASC;QACP,qBACE,KAACR;sBACC,cAAA,KAACS;;IAGP;IAEA,SAASC;QACP,OAAOC,oBACLnB,MACAC,cACAE,4BACAG,+BACAC,WACAF;IAEJ;IAEA,eAAeO;QACb,IAAI;YACF,OAAO,MAAMM;QACf,EAAE,OAAOE,OAAO;YACd,IAAI,CAACf,aAAaZ,0BAA0B2B,QAAQ;gBAClD,IAAI;oBACF,OAAO,MAAMC,oBACXrB,MACAC,cACAE,4BACAG,+BACAC;gBAEJ,EAAE,OAAM,CAAC;YACX;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;QACT;IACF;IACAK,SAASU,WAAW,GAAG3B;IAEvB,SAAS4B;QACP,OAAOC,oBACLxB,MACAC,cACAE,4BACAD,iBACAI,+BACAC,WACAF;IAEJ;IAEA,eAAeoB;QACb,IAAIC;QACJ,IAAIN,QAAQ;QACZ,IAAI;YACFM,SAAS,MAAMH;YACf,OAAO;gBACLA,UAAUG;gBACVN,OAAO;gBACPO,QAAQC;YACV;QACF,EAAE,OAAOC,aAAa;YACpBT,QAAQS;YACR,IAAI,CAACxB,aAAaZ,0BAA0BoC,cAAc;gBACxD,IAAI;oBACFH,SAAS,MAAMI,oBACb9B,MACAC,cACAE,4BACAD,iBACAI,+BACAC;oBAEF,OAAO;wBACLgB,UAAUG;wBACVN;wBACAO,MAAM,EAAGP,yBAAD,AAACA,MAAeO,MAAM;oBAChC;gBACF,EAAE,OAAOI,qBAAqB;oBAC5BX,QAAQW;oBACR,8DAA8D;oBAC9D,8EAA8E;oBAC9E,IAAIrB,0BAA0BZ,WAAWiC,sBAAsB;wBAC7D,MAAMA;oBACR;gBACF;YACF;YACA,8DAA8D;YAC9D,8EAA8E;YAC9E,IAAIrB,0BAA0BZ,WAAW+B,cAAc;gBACrD,MAAMA;YACR;YACA,6DAA6D;YAC7D,6DAA6D;YAC7D,iEAAiE;YACjE,yBAAyB;YACzB,OAAO;gBACLN,UAAUG;gBACVN;gBACAO,MAAM,EAAGP,yBAAD,AAACA,MAAeO,MAAM;YAChC;QACF;IACF;IACA,eAAeV;QACb,MAAMe,UAAUP;QAChB,IAAIf,wBAAwB;YAC1B,qBACE,KAACnC;gBAAS0D,UAAU;0BAClB,cAAA,KAACrC;oBAAcoC,SAASA;;;QAG9B;QACA,MAAME,gBAAgB,MAAMF;QAC5B,OAAOE,cAAcX,QAAQ;IAC/B;IAEAN,SAASK,WAAW,GAAG5B;IAEvB,eAAeyC;QACb,4DAA4D;QAC5D,iEAAiE;QACjE,IAAI,CAACzB,wBAAwB;YAC3B,MAAMa;QACR;QACA,OAAOK;IACT;IAEA,eAAeQ;QACb,MAAMlB;QACN,OAAOU;IACT;IAEA,SAASS;QACP,IAAI3B,wBAAwB;YAC1B,qBAAO,KAACb;gBAAoBmC,SAASP;;QACvC;QACA,OAAO;IACT;IAEA,OAAO;QACLd;QACAK;QACAoB;QACAD;QACAE;IACF;AACF;AAEA,MAAMb,sBAAsBhD,MAAM8D;AAClC,eAAeA,wBACbtC,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDD,eAAgC,EAChCI,6BAA4D,EAC5DC,SAAoB,EACpBF,SAA0C;IAE1C,MAAMkC,kBAAkBlC,cAAc,aAAauB,YAAYvB;IAC/D,OAAOmC,eACLxC,MACAC,cACAE,4BACAD,iBACAI,+BACAC,WACAgC;AAEJ;AAEA,MAAMT,sBAAsBtD,MAAMiE;AAClC,eAAeA,wBACbzC,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDD,eAAgC,EAChCI,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAMmC,0BAA0B;IAChC,OAAOF,eACLxC,MACAC,cACAE,4BACAD,iBACAI,+BACAC,WACAmC;AAEJ;AAEA,MAAMvB,sBAAsB3C,MAAMmE;AAClC,eAAeA,wBACb3C,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDG,6BAA4D,EAC5DC,SAAoB,EACpBF,SAA0C;IAE1C,MAAMkC,kBAAkBlC,cAAc,aAAauB,YAAYvB;IAC/D,OAAOuC,eACL5C,MACAC,cACAE,4BACAG,+BACAC,WACAgC;AAEJ;AAEA,MAAMlB,sBAAsB7C,MAAMqE;AAClC,eAAeA,wBACb7C,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDG,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAMmC,0BAA0B;IAChC,OAAOE,eACL5C,MACAC,cACAE,4BACAG,+BACAC,WACAmC;AAEJ;AAEA,eAAeF,eACbxC,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDD,eAAgC,EAChCI,6BAA4D,EAC5DC,SAAoB,EACpBgC,eAAmC;IAEnC,MAAMO,mBAAmB,MAAMxD,gBAC7BU,MACAC,cACAsC,iBACApC,4BACAG,+BACAC,WACAL;IAEF,MAAM6C,WACJC,uBAAuBF;IACzB,qBACE;kBACGC,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAO1E,aAAayE,IAA0B;gBAAEE,KAAKD;YAAM;QAC7D;;AAGN;AAEA,eAAeP,eACb5C,IAAgB,EAChBC,YAAqC,EACrCE,0BAAsD,EACtDG,6BAA4D,EAC5DC,SAAoB,EACpBgC,eAAmC;IAEnC,MAAMc,2BAA2B,MAAM9D,gBACrCS,MACAC,cACAsC,iBACApC,4BACAG,+BACAC;IAGF,MAAMwC,WAAmCO,uBACvCD;IAEF,qBACE;kBACGN,SAASE,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAO1E,aAAayE,IAA0B;gBAAEE,KAAKD;YAAM;QAC7D;;AAGN;AAEA,SAASH,uBAAuBzB,QAA0B;IACxD,OAAO/B,WAAW;QAChBX,UAAU;YAAE0C;QAAS;QACrBtC,mBAAmB;YAAEsE,YAAYhC,SAASgC,UAAU;QAAC;QACrD3E,WAAW;YAAE4E,QAAQjC,SAASiC,MAAM;QAAC;QACrCxE,aAAa;YAAEyE,UAAUlC,SAASkC,QAAQ;QAAC;QAC3C9E,oBAAoB;YAAE+E,iBAAiBnC,SAASmC,eAAe;QAAC;QAChE3E,iBAAiB;YAAE4E,cAAcpC,SAASoC,YAAY;QAAC;QACvDjF,gBAAgB;YAAEkF,aAAarC,SAASqC,WAAW;QAAC;QACpD1E,kBAAkB;YAAE2E,WAAWtC,SAASsC,SAAS;QAAC;QAClD1E,gBAAgB;YAAE2E,SAASvC,SAASuC,OAAO;QAAC;QAC5C1E,aAAa;YAAE2E,UAAUxC,SAASwC,QAAQ;QAAC;QAC3C1E,cAAc;YAAE2E,OAAOzC,SAASyC,KAAK;QAAC;KACvC;AACH;AAEA,SAASV,uBAAuBpC,QAA0B;IACxD,OAAO1B,WAAW;QAACV,aAAa;YAAEoC,UAAUA;QAAS;KAAG;AAC1D"}