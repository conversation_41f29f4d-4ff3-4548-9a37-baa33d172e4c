{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport type { MetadataContext } from '../types/resolvers'\n\nfunction isStringOrURL(icon: any): icon is string | URL {\n  return typeof icon === 'string' || icon instanceof URL\n}\n\nfunction createLocalMetadataBase() {\n  return new URL(`http://localhost:${process.env.PORT || 3000}`)\n}\n\nfunction getPreviewDeploymentUrl(): URL | undefined {\n  const origin = process.env.VERCEL_BRANCH_URL || process.env.VERCEL_URL\n  return origin ? new URL(`https://${origin}`) : undefined\n}\n\nfunction getProductionDeploymentUrl(): URL | undefined {\n  const origin = process.env.VERCEL_PROJECT_PRODUCTION_URL\n  return origin ? new URL(`https://${origin}`) : undefined\n}\n\n/**\n * Given an optional user-provided metadataBase, this determines what the metadataBase should\n * fallback to. Specifically:\n * - In dev, it should always be localhost\n * - In Vercel preview builds, it should be the preview build ID\n * - In start, it should be the user-provided metadataBase value. Otherwise,\n * it'll fall back to the Vercel production deployment, and localhost as a last resort.\n */\nexport function getSocialImageMetadataBaseFallback(\n  metadataBase: URL | null\n): URL {\n  const defaultMetadataBase = createLocalMetadataBase()\n  const previewDeploymentUrl = getPreviewDeploymentUrl()\n  const productionDeploymentUrl = getProductionDeploymentUrl()\n\n  let fallbackMetadataBase\n  if (process.env.NODE_ENV === 'development') {\n    fallbackMetadataBase = defaultMetadataBase\n  } else {\n    fallbackMetadataBase =\n      process.env.NODE_ENV === 'production' &&\n      previewDeploymentUrl &&\n      process.env.VERCEL_ENV === 'preview'\n        ? previewDeploymentUrl\n        : metadataBase || productionDeploymentUrl || defaultMetadataBase\n  }\n\n  return fallbackMetadataBase\n}\n\nfunction resolveUrl(url: null | undefined, metadataBase: URL | null): null\nfunction resolveUrl(url: string | URL, metadataBase: URL | null): URL\nfunction resolveUrl(\n  url: string | URL | null | undefined,\n  metadataBase: URL | null\n): URL | null\nfunction resolveUrl(\n  url: string | URL | null | undefined,\n  metadataBase: URL | null\n): URL | null {\n  if (url instanceof URL) return url\n  if (!url) return null\n\n  try {\n    // If we can construct a URL instance from url, ignore metadataBase\n    const parsedUrl = new URL(url)\n    return parsedUrl\n  } catch {}\n\n  if (!metadataBase) {\n    metadataBase = createLocalMetadataBase()\n  }\n\n  // Handle relative or absolute paths\n  const pathname = metadataBase.pathname || ''\n  const joinedPath = path.posix.join(pathname, url)\n\n  return new URL(joinedPath, metadataBase)\n}\n\n// Resolve with `pathname` if `url` is a relative path.\nfunction resolveRelativeUrl(url: string | URL, pathname: string): string | URL {\n  if (typeof url === 'string' && url.startsWith('./')) {\n    return path.posix.resolve(pathname, url)\n  }\n  return url\n}\n\n// The regex is matching logic from packages/next/src/lib/load-custom-routes.ts\nconst FILE_REGEX =\n  /^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/]+\\.\\w+))(\\/?|$)/i\nfunction isFilePattern(pathname: string): boolean {\n  return FILE_REGEX.test(pathname)\n}\n\n// Resolve `pathname` if `url` is a relative path the compose with `metadataBase`.\nfunction resolveAbsoluteUrlWithPathname(\n  url: string | URL,\n  metadataBase: URL | null,\n  { trailingSlash, pathname }: MetadataContext\n): string {\n  // Resolve url with pathname that always starts with `/`\n  url = resolveRelativeUrl(url, pathname)\n\n  // Convert string url or URL instance to absolute url string,\n  // if there's case needs to be resolved with metadataBase\n  let resolvedUrl = ''\n  const result = metadataBase ? resolveUrl(url, metadataBase) : url\n  if (typeof result === 'string') {\n    resolvedUrl = result\n  } else {\n    resolvedUrl = result.pathname === '/' ? result.origin : result.href\n  }\n\n  // Add trailing slash if it's enabled for urls matches the condition\n  // - Not external, same origin with metadataBase\n  // - Doesn't have query\n  if (trailingSlash && !resolvedUrl.endsWith('/')) {\n    let isRelative = resolvedUrl.startsWith('/')\n    let hasQuery = resolvedUrl.includes('?')\n    let isExternal = false\n    let isFileUrl = false\n\n    if (!isRelative) {\n      try {\n        const parsedUrl = new URL(resolvedUrl)\n        isExternal =\n          metadataBase != null && parsedUrl.origin !== metadataBase.origin\n        isFileUrl = isFilePattern(parsedUrl.pathname)\n      } catch {\n        // If it's not a valid URL, treat it as external\n        isExternal = true\n      }\n      if (\n        // Do not apply trailing slash for file like urls, aligning with the behavior with `trailingSlash`\n        !isFileUrl &&\n        !isExternal &&\n        !hasQuery\n      )\n        return `${resolvedUrl}/`\n    }\n  }\n\n  return resolvedUrl\n}\n\nexport {\n  isStringOrURL,\n  resolveUrl,\n  resolveRelativeUrl,\n  resolveAbsoluteUrlWithPathname,\n}\n"], "names": ["path", "isStringOrURL", "icon", "URL", "createLocalMetadataBase", "process", "env", "PORT", "getPreviewDeploymentUrl", "origin", "VERCEL_BRANCH_URL", "VERCEL_URL", "undefined", "getProductionDeploymentUrl", "VERCEL_PROJECT_PRODUCTION_URL", "getSocialImageMetadataBaseFallback", "metadataBase", "defaultMetadataBase", "previewDeploymentUrl", "productionDeploymentUrl", "fallbackMetadataBase", "NODE_ENV", "VERCEL_ENV", "resolveUrl", "url", "parsedUrl", "pathname", "joinedPath", "posix", "join", "resolveRelativeUrl", "startsWith", "resolve", "FILE_REGEX", "isFilePattern", "test", "resolveAbsoluteUrlWithPathname", "trailingSlash", "resolvedUrl", "result", "href", "endsWith", "isRelative", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isExternal", "isFileUrl"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AAGtD,SAASC,cAAcC,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,OAAO,IAAID,IAAI,CAAC,iBAAiB,EAAEE,QAAQC,GAAG,CAACC,IAAI,IAAI,MAAM;AAC/D;AAEA,SAASC;IACP,MAAMC,SAASJ,QAAQC,GAAG,CAACI,iBAAiB,IAAIL,QAAQC,GAAG,CAACK,UAAU;IACtE,OAAOF,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,QAAQ,IAAIG;AACjD;AAEA,SAASC;IACP,MAAMJ,SAASJ,QAAQC,GAAG,CAACQ,6BAA6B;IACxD,OAAOL,SAAS,IAAIN,IAAI,CAAC,QAAQ,EAAEM,QAAQ,IAAIG;AACjD;AAEA;;;;;;;CAOC,GACD,OAAO,SAASG,mCACdC,YAAwB;IAExB,MAAMC,sBAAsBb;IAC5B,MAAMc,uBAAuBV;IAC7B,MAAMW,0BAA0BN;IAEhC,IAAIO;IACJ,IAAIf,QAAQC,GAAG,CAACe,QAAQ,KAAK,eAAe;QAC1CD,uBAAuBH;IACzB,OAAO;QACLG,uBACEf,QAAQC,GAAG,CAACe,QAAQ,KAAK,gBACzBH,wBACAb,QAAQC,GAAG,CAACgB,UAAU,KAAK,YACvBJ,uBACAF,gBAAgBG,2BAA2BF;IACnD;IAEA,OAAOG;AACT;AAQA,SAASG,WACPC,GAAoC,EACpCR,YAAwB;IAExB,IAAIQ,eAAerB,KAAK,OAAOqB;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAItB,IAAIqB;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACT,cAAc;QACjBA,eAAeZ;IACjB;IAEA,oCAAoC;IACpC,MAAMsB,WAAWV,aAAaU,QAAQ,IAAI;IAC1C,MAAMC,aAAa3B,KAAK4B,KAAK,CAACC,IAAI,CAACH,UAAUF;IAE7C,OAAO,IAAIrB,IAAIwB,YAAYX;AAC7B;AAEA,uDAAuD;AACvD,SAASc,mBAAmBN,GAAiB,EAAEE,QAAgB;IAC7D,IAAI,OAAOF,QAAQ,YAAYA,IAAIO,UAAU,CAAC,OAAO;QACnD,OAAO/B,KAAK4B,KAAK,CAACI,OAAO,CAACN,UAAUF;IACtC;IACA,OAAOA;AACT;AAEA,+EAA+E;AAC/E,MAAMS,aACJ;AACF,SAASC,cAAcR,QAAgB;IACrC,OAAOO,WAAWE,IAAI,CAACT;AACzB;AAEA,kFAAkF;AAClF,SAASU,+BACPZ,GAAiB,EACjBR,YAAwB,EACxB,EAAEqB,aAAa,EAAEX,QAAQ,EAAmB;IAE5C,wDAAwD;IACxDF,MAAMM,mBAAmBN,KAAKE;IAE9B,6DAA6D;IAC7D,yDAAyD;IACzD,IAAIY,cAAc;IAClB,MAAMC,SAASvB,eAAeO,WAAWC,KAAKR,gBAAgBQ;IAC9D,IAAI,OAAOe,WAAW,UAAU;QAC9BD,cAAcC;IAChB,OAAO;QACLD,cAAcC,OAAOb,QAAQ,KAAK,MAAMa,OAAO9B,MAAM,GAAG8B,OAAOC,IAAI;IACrE;IAEA,oEAAoE;IACpE,gDAAgD;IAChD,uBAAuB;IACvB,IAAIH,iBAAiB,CAACC,YAAYG,QAAQ,CAAC,MAAM;QAC/C,IAAIC,aAAaJ,YAAYP,UAAU,CAAC;QACxC,IAAIY,WAAWL,YAAYM,QAAQ,CAAC;QACpC,IAAIC,aAAa;QACjB,IAAIC,YAAY;QAEhB,IAAI,CAACJ,YAAY;YACf,IAAI;gBACF,MAAMjB,YAAY,IAAItB,IAAImC;gBAC1BO,aACE7B,gBAAgB,QAAQS,UAAUhB,MAAM,KAAKO,aAAaP,MAAM;gBAClEqC,YAAYZ,cAAcT,UAAUC,QAAQ;YAC9C,EAAE,OAAM;gBACN,gDAAgD;gBAChDmB,aAAa;YACf;YACA,IACE,kGAAkG;YAClG,CAACC,aACD,CAACD,cACD,CAACF,UAED,OAAO,GAAGL,YAAY,CAAC,CAAC;QAC5B;IACF;IAEA,OAAOA;AACT;AAEA,SACErC,aAAa,EACbsB,UAAU,EACVO,kBAAkB,EAClBM,8BAA8B,KAC/B"}