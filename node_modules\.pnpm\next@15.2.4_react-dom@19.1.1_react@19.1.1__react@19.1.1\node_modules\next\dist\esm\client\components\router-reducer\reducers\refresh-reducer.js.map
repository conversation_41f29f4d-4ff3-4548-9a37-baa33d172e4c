{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "handleSegmentMismatch", "hasInterceptionRouteInCurrentTree", "refreshInactiveParallelSegments", "revalidateEntireCache", "refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "includeNextUrl", "lazyData", "URL", "flightRouterState", "nextUrl", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "canonicalUrlOverrideHref", "undefined", "rsc", "loading", "prefetchRsc", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchCache", "Map", "updatedTree", "updatedCache", "patchedTree"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBf;IAEzB,sFAAsF;IACtF,sHAAsH;IACtH,MAAMgB,iBAAiBd,kCAAkCI,MAAMO,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCE,MAAME,QAAQ,GAAGxB,oBAAoB,IAAIyB,IAAIR,MAAMF,SAAS;QAC1DW,mBAAmB;YACjBP,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACdA,WAAW,CAAC,EAAE;YACd;SACD;QACDQ,SAASJ,iBAAiBV,MAAMc,OAAO,GAAG;IAC5C;IAEA,OAAOL,MAAME,QAAQ,CAACI,IAAI,CACxB;YAAO,EAAEC,UAAU,EAAEX,cAAcY,oBAAoB,EAAE;QACvD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOzB,kBACLS,OACAG,SACAa,YACAhB,MAAMkB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DV,MAAME,QAAQ,GAAG;QAEjB,KAAK,MAAMS,wBAAwBJ,WAAY;YAC7C,MAAM,EACJT,MAAMc,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO3B;YACT;YAEA,MAAM4B,UAAUvC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJiB,aACAe,WACArB,MAAMK,YAAY;YAGpB,IAAIuB,YAAY,MAAM;gBACpB,OAAOjC,sBAAsBK,OAAOC,QAAQoB;YAC9C;YAEA,IAAI/B,4BAA4BgB,aAAasB,UAAU;gBACrD,OAAOrC,kBACLS,OACAG,SACAC,MACAJ,MAAMkB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMU,2BAA2BZ,uBAC7B7B,kBAAkB6B,wBAClBa;YAEJ,IAAIb,sBAAsB;gBACxBd,QAAQE,YAAY,GAAGwB;YACzB;YAEA,8FAA8F;YAC9F,IAAIN,sBAAsB,MAAM;gBAC9B,MAAMQ,MAAMR,iBAAiB,CAAC,EAAE;gBAChC,MAAMS,UAAUT,iBAAiB,CAAC,EAAE;gBACpCd,MAAMsB,GAAG,GAAGA;gBACZtB,MAAMwB,WAAW,GAAG;gBACpBxB,MAAMuB,OAAO,GAAGA;gBAChBvC,8BACEgB,OACA,4FAA4F;gBAC5FqB,WACAT,WACAE,mBACAC,MACAM;gBAEF,IAAII,QAAQC,GAAG,CAACC,2BAA2B,EAAE;oBAC3CtC,sBAAsBE,MAAMc,OAAO,EAAEc;gBACvC,OAAO;oBACLzB,QAAQkC,aAAa,GAAG,IAAIC;gBAC9B;YACF;YAEA,MAAMzC,gCAAgC;gBACpCG;gBACAuC,aAAaX;gBACbY,cAAc/B;gBACdC;gBACAL,cAAcF,QAAQE,YAAY,IAAIL,MAAMK,YAAY;YAC1D;YAEAF,QAAQM,KAAK,GAAGA;YAChBN,QAAQsC,WAAW,GAAGb;YAEtBtB,cAAcsB;QAChB;QAEA,OAAOpC,cAAcQ,OAAOG;IAC9B,GACA,IAAMH;AAEV"}