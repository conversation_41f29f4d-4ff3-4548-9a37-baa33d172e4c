{"version": 3, "sources": ["../../src/client/get-domain-locale.ts"], "sourcesContent": ["import type { DomainLocale } from '../server/config'\nimport type { normalizeLocalePath as NormalizeFn } from './normalize-locale-path'\nimport type { detectDomainLocale as DetectFn } from './detect-domain-locale'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function getDomainLocale(\n  path: string,\n  locale?: string | false,\n  locales?: readonly string[],\n  domainLocales?: readonly DomainLocale[]\n) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const normalizeLocalePath: typeof NormalizeFn =\n      require('./normalize-locale-path').normalizeLocalePath\n    const detectDomainLocale: typeof DetectFn =\n      require('./detect-domain-locale').detectDomainLocale\n\n    const target = locale || normalizeLocalePath(path, locales).detectedLocale\n    const domain = detectDomainLocale(domainLocales, undefined, target)\n    if (domain) {\n      const proto = `http${domain.http ? '' : 's'}://`\n      const finalLocale = target === domain.defaultLocale ? '' : `/${target}`\n      return `${proto}${domain.domain}${normalizePathTrailingSlash(\n        `${basePath}${finalLocale}${path}`\n      )}`\n    }\n    return false\n  } else {\n    return false\n  }\n}\n"], "names": ["normalizePathTrailingSlash", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "getDomainLocale", "path", "locale", "locales", "domainLocales", "__NEXT_I18N_SUPPORT", "normalizeLocalePath", "require", "detectDomainLocale", "target", "detectedLocale", "domain", "undefined", "proto", "http", "finalLocale", "defaultLocale"], "mappings": "AAGA,SAASA,0BAA0B,QAAQ,6BAA4B;AAEvE,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAEnE,OAAO,SAASC,gBACdC,IAAY,EACZC,MAAuB,EACvBC,OAA2B,EAC3BC,aAAuC;IAEvC,IAAIP,QAAQC,GAAG,CAACO,mBAAmB,EAAE;QACnC,MAAMC,sBACJC,QAAQ,2BAA2BD,mBAAmB;QACxD,MAAME,qBACJD,QAAQ,0BAA0BC,kBAAkB;QAEtD,MAAMC,SAASP,UAAUI,oBAAoBL,MAAME,SAASO,cAAc;QAC1E,MAAMC,SAASH,mBAAmBJ,eAAeQ,WAAWH;QAC5D,IAAIE,QAAQ;YACV,MAAME,QAAQ,AAAC,SAAMF,CAAAA,OAAOG,IAAI,GAAG,KAAK,GAAE,IAAE;YAC5C,MAAMC,cAAcN,WAAWE,OAAOK,aAAa,GAAG,KAAK,AAAC,MAAGP;YAC/D,OAAO,AAAC,KAAEI,QAAQF,OAAOA,MAAM,GAAGhB,2BAChC,AAAC,KAAEC,WAAWmB,cAAcd;QAEhC;QACA,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF"}