{"version": 3, "sources": ["../../src/client/has-base-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n"], "names": ["pathHasPrefix", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "has<PERSON>ase<PERSON><PERSON>", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6CAA4C;AAE1E,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAEnE,OAAO,SAASC,YAAYC,IAAY;IACtC,OAAON,cAAcM,MAAML;AAC7B"}