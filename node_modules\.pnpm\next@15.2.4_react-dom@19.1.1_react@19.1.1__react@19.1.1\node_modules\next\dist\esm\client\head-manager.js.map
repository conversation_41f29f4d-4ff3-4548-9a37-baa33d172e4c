{"version": 3, "sources": ["../../src/client/head-manager.ts"], "sourcesContent": ["import { setAttributesFromProps } from './set-attributes-from-props'\n\nimport type { JSX } from 'react'\n\nfunction reactElementToDOM({ type, props }: JSX.Element): HTMLElement {\n  const el: HTMLElement = document.createElement(type)\n  setAttributesFromProps(el, props)\n\n  const { children, dangerouslySetInnerHTML } = props\n  if (dangerouslySetInnerHTML) {\n    el.innerHTML = dangerouslySetInnerHTML.__html || ''\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n  }\n  return el\n}\n\n/**\n * When a `nonce` is present on an element, browsers such as Chrome and Firefox strip it out of the\n * actual HTML attributes for security reasons *when the element is added to the document*. Thus,\n * given two equivalent elements that have nonces, `Element,isEqualNode()` will return false if one\n * of those elements gets added to the document. Although the `element.nonce` property will be the\n * same for both elements, the one that was added to the document will return an empty string for\n * its nonce HTML attribute value.\n *\n * This custom `isEqualNode()` function therefore removes the nonce value from the `newTag` before\n * comparing it to `oldTag`, restoring it afterwards.\n *\n * For more information, see:\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1211471#c12\n */\nexport function isEqualNode(oldTag: Element, newTag: Element) {\n  if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n    const nonce = newTag.getAttribute('nonce')\n    // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n    // be stripped if there is no content security policy response header that includes a nonce.\n    if (nonce && !oldTag.getAttribute('nonce')) {\n      const cloneTag = newTag.cloneNode(true) as typeof newTag\n      cloneTag.setAttribute('nonce', '')\n      cloneTag.nonce = nonce\n      return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag)\n    }\n  }\n\n  return oldTag.isEqualNode(newTag)\n}\n\nlet updateElements: (type: string, components: JSX.Element[]) => void\n\nif (process.env.__NEXT_STRICT_NEXT_HEAD) {\n  updateElements = (type, components) => {\n    const headEl = document.querySelector('head')\n    if (!headEl) return\n\n    const oldTags = new Set(headEl.querySelectorAll(`${type}[data-next-head]`))\n\n    if (type === 'meta') {\n      const metaCharset = headEl.querySelector('meta[charset]')\n      if (metaCharset !== null) {\n        oldTags.add(metaCharset)\n      }\n    }\n\n    const newTags: Element[] = []\n    for (let i = 0; i < components.length; i++) {\n      const component = components[i]\n      const newTag = reactElementToDOM(component)\n      newTag.setAttribute('data-next-head', '')\n\n      let isNew = true\n      for (const oldTag of oldTags) {\n        if (isEqualNode(oldTag, newTag)) {\n          oldTags.delete(oldTag)\n          isNew = false\n          break\n        }\n      }\n\n      if (isNew) {\n        newTags.push(newTag)\n      }\n    }\n\n    for (const oldTag of oldTags) {\n      oldTag.parentNode?.removeChild(oldTag)\n    }\n\n    for (const newTag of newTags) {\n      // meta[charset] must be first element so special case\n      if (\n        newTag.tagName.toLowerCase() === 'meta' &&\n        newTag.getAttribute('charset') !== null\n      ) {\n        headEl.prepend(newTag)\n      }\n      headEl.appendChild(newTag)\n    }\n  }\n} else {\n  updateElements = (type, components) => {\n    const headEl = document.getElementsByTagName('head')[0]\n    const headCountEl: HTMLMetaElement = headEl.querySelector(\n      'meta[name=next-head-count]'\n    ) as HTMLMetaElement\n    if (process.env.NODE_ENV !== 'production') {\n      if (!headCountEl) {\n        console.error(\n          'Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing'\n        )\n        return\n      }\n    }\n\n    const headCount = Number(headCountEl.content)\n    const oldTags: Element[] = []\n\n    for (\n      let i = 0, j = headCountEl.previousElementSibling;\n      i < headCount;\n      i++, j = j?.previousElementSibling || null\n    ) {\n      if (j?.tagName?.toLowerCase() === type) {\n        oldTags.push(j)\n      }\n    }\n    const newTags = (components.map(reactElementToDOM) as HTMLElement[]).filter(\n      (newTag) => {\n        for (let k = 0, len = oldTags.length; k < len; k++) {\n          const oldTag = oldTags[k]\n          if (isEqualNode(oldTag, newTag)) {\n            oldTags.splice(k, 1)\n            return false\n          }\n        }\n        return true\n      }\n    )\n\n    oldTags.forEach((t) => t.parentNode?.removeChild(t))\n    newTags.forEach((t) => headEl.insertBefore(t, headCountEl))\n    headCountEl.content = (\n      headCount -\n      oldTags.length +\n      newTags.length\n    ).toString()\n  }\n}\n\nexport default function initHeadManager(): {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n} {\n  return {\n    mountedInstances: new Set(),\n    updateHead: (head: JSX.Element[]) => {\n      const tags: Record<string, JSX.Element[]> = {}\n\n      head.forEach((h) => {\n        if (\n          // If the font tag is loaded only on client navigation\n          // it won't be inlined. In this case revert to the original behavior\n          h.type === 'link' &&\n          h.props['data-optimized-fonts']\n        ) {\n          if (\n            document.querySelector(`style[data-href=\"${h.props['data-href']}\"]`)\n          ) {\n            return\n          } else {\n            h.props.href = h.props['data-href']\n            h.props['data-href'] = undefined\n          }\n        }\n\n        const components = tags[h.type] || []\n        components.push(h)\n        tags[h.type] = components\n      })\n\n      const titleComponent = tags.title ? tags.title[0] : null\n      let title = ''\n      if (titleComponent) {\n        const { children } = titleComponent.props\n        title =\n          typeof children === 'string'\n            ? children\n            : Array.isArray(children)\n              ? children.join('')\n              : ''\n      }\n      if (title !== document.title) document.title = title\n      ;['meta', 'base', 'link', 'style', 'script'].forEach((type) => {\n        updateElements(type, tags[type] || [])\n      })\n    },\n  }\n}\n"], "names": ["setAttributesFromProps", "reactElementToDOM", "type", "props", "el", "document", "createElement", "children", "dangerouslySetInnerHTML", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "isEqualNode", "oldTag", "newTag", "HTMLElement", "nonce", "getAttribute", "cloneTag", "cloneNode", "setAttribute", "updateElements", "process", "env", "__NEXT_STRICT_NEXT_HEAD", "components", "headEl", "querySelector", "oldTags", "Set", "querySelectorAll", "metaCharset", "add", "newTags", "i", "length", "component", "isNew", "delete", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "tagName", "toLowerCase", "prepend", "append<PERSON><PERSON><PERSON>", "getElementsByTagName", "headCountEl", "NODE_ENV", "console", "error", "headCount", "Number", "content", "j", "previousElementSibling", "map", "filter", "k", "len", "splice", "for<PERSON>ach", "t", "insertBefore", "toString", "initHeadManager", "mountedInstances", "updateHead", "head", "tags", "h", "href", "undefined", "titleComponent", "title"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,8BAA6B;AAIpE,SAASC,kBAAkB,KAA4B;IAA5B,IAAA,EAAEC,IAAI,EAAEC,KAAK,EAAe,GAA5B;IACzB,MAAMC,KAAkBC,SAASC,aAAa,CAACJ;IAC/CF,uBAAuBI,IAAID;IAE3B,MAAM,EAAEI,QAAQ,EAAEC,uBAAuB,EAAE,GAAGL;IAC9C,IAAIK,yBAAyB;QAC3BJ,GAAGK,SAAS,GAAGD,wBAAwBE,MAAM,IAAI;IACnD,OAAO,IAAIH,UAAU;QACnBH,GAAGO,WAAW,GACZ,OAAOJ,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACZA,SAASO,IAAI,CAAC,MACd;IACV;IACA,OAAOV;AACT;AAEA;;;;;;;;;;;;;CAaC,GACD,OAAO,SAASW,YAAYC,MAAe,EAAEC,MAAe;IAC1D,IAAID,kBAAkBE,eAAeD,kBAAkBC,aAAa;QAClE,MAAMC,QAAQF,OAAOG,YAAY,CAAC;QAClC,8FAA8F;QAC9F,4FAA4F;QAC5F,IAAID,SAAS,CAACH,OAAOI,YAAY,CAAC,UAAU;YAC1C,MAAMC,WAAWJ,OAAOK,SAAS,CAAC;YAClCD,SAASE,YAAY,CAAC,SAAS;YAC/BF,SAASF,KAAK,GAAGA;YACjB,OAAOA,UAAUH,OAAOG,KAAK,IAAIH,OAAOD,WAAW,CAACM;QACtD;IACF;IAEA,OAAOL,OAAOD,WAAW,CAACE;AAC5B;AAEA,IAAIO;AAEJ,IAAIC,QAAQC,GAAG,CAACC,uBAAuB,EAAE;IACvCH,iBAAiB,CAACtB,MAAM0B;QACtB,MAAMC,SAASxB,SAASyB,aAAa,CAAC;QACtC,IAAI,CAACD,QAAQ;QAEb,MAAME,UAAU,IAAIC,IAAIH,OAAOI,gBAAgB,CAAC,AAAC,KAAE/B,OAAK;QAExD,IAAIA,SAAS,QAAQ;YACnB,MAAMgC,cAAcL,OAAOC,aAAa,CAAC;YACzC,IAAII,gBAAgB,MAAM;gBACxBH,QAAQI,GAAG,CAACD;YACd;QACF;QAEA,MAAME,UAAqB,EAAE;QAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIT,WAAWU,MAAM,EAAED,IAAK;YAC1C,MAAME,YAAYX,UAAU,CAACS,EAAE;YAC/B,MAAMpB,SAAShB,kBAAkBsC;YACjCtB,OAAOM,YAAY,CAAC,kBAAkB;YAEtC,IAAIiB,QAAQ;YACZ,KAAK,MAAMxB,UAAUe,QAAS;gBAC5B,IAAIhB,YAAYC,QAAQC,SAAS;oBAC/Bc,QAAQU,MAAM,CAACzB;oBACfwB,QAAQ;oBACR;gBACF;YACF;YAEA,IAAIA,OAAO;gBACTJ,QAAQM,IAAI,CAACzB;YACf;QACF;QAEA,KAAK,MAAMD,UAAUe,QAAS;gBAC5Bf;aAAAA,qBAAAA,OAAO2B,UAAU,qBAAjB3B,mBAAmB4B,WAAW,CAAC5B;QACjC;QAEA,KAAK,MAAMC,UAAUmB,QAAS;YAC5B,sDAAsD;YACtD,IACEnB,OAAO4B,OAAO,CAACC,WAAW,OAAO,UACjC7B,OAAOG,YAAY,CAAC,eAAe,MACnC;gBACAS,OAAOkB,OAAO,CAAC9B;YACjB;YACAY,OAAOmB,WAAW,CAAC/B;QACrB;IACF;AACF,OAAO;IACLO,iBAAiB,CAACtB,MAAM0B;QACtB,MAAMC,SAASxB,SAAS4C,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACvD,MAAMC,cAA+BrB,OAAOC,aAAa,CACvD;QAEF,IAAIL,QAAQC,GAAG,CAACyB,QAAQ,KAAK,cAAc;YACzC,IAAI,CAACD,aAAa;gBAChBE,QAAQC,KAAK,CACX;gBAEF;YACF;QACF;QAEA,MAAMC,YAAYC,OAAOL,YAAYM,OAAO;QAC5C,MAAMzB,UAAqB,EAAE;QAE7B,IACE,IAAIM,IAAI,GAAGoB,IAAIP,YAAYQ,sBAAsB,EACjDrB,IAAIiB,WACJjB,KAAKoB,IAAIA,CAAAA,qBAAAA,EAAGC,sBAAsB,KAAI,KACtC;gBACID;YAAJ,IAAIA,CAAAA,sBAAAA,aAAAA,EAAGZ,OAAO,qBAAVY,WAAYX,WAAW,QAAO5C,MAAM;gBACtC6B,QAAQW,IAAI,CAACe;YACf;QACF;QACA,MAAMrB,UAAU,AAACR,WAAW+B,GAAG,CAAC1D,mBAAqC2D,MAAM,CACzE,CAAC3C;YACC,IAAK,IAAI4C,IAAI,GAAGC,MAAM/B,QAAQO,MAAM,EAAEuB,IAAIC,KAAKD,IAAK;gBAClD,MAAM7C,SAASe,OAAO,CAAC8B,EAAE;gBACzB,IAAI9C,YAAYC,QAAQC,SAAS;oBAC/Bc,QAAQgC,MAAM,CAACF,GAAG;oBAClB,OAAO;gBACT;YACF;YACA,OAAO;QACT;QAGF9B,QAAQiC,OAAO,CAAC,CAACC;gBAAMA;oBAAAA,gBAAAA,EAAEtB,UAAU,qBAAZsB,cAAcrB,WAAW,CAACqB;;QACjD7B,QAAQ4B,OAAO,CAAC,CAACC,IAAMpC,OAAOqC,YAAY,CAACD,GAAGf;QAC9CA,YAAYM,OAAO,GAAG,AACpBF,CAAAA,YACAvB,QAAQO,MAAM,GACdF,QAAQE,MAAM,AAAD,EACb6B,QAAQ;IACZ;AACF;AAEA,eAAe,SAASC;IAItB,OAAO;QACLC,kBAAkB,IAAIrC;QACtBsC,YAAY,CAACC;YACX,MAAMC,OAAsC,CAAC;YAE7CD,KAAKP,OAAO,CAAC,CAACS;gBACZ,IACE,sDAAsD;gBACtD,oEAAoE;gBACpEA,EAAEvE,IAAI,KAAK,UACXuE,EAAEtE,KAAK,CAAC,uBAAuB,EAC/B;oBACA,IACEE,SAASyB,aAAa,CAAC,AAAC,sBAAmB2C,EAAEtE,KAAK,CAAC,YAAY,GAAC,OAChE;wBACA;oBACF,OAAO;wBACLsE,EAAEtE,KAAK,CAACuE,IAAI,GAAGD,EAAEtE,KAAK,CAAC,YAAY;wBACnCsE,EAAEtE,KAAK,CAAC,YAAY,GAAGwE;oBACzB;gBACF;gBAEA,MAAM/C,aAAa4C,IAAI,CAACC,EAAEvE,IAAI,CAAC,IAAI,EAAE;gBACrC0B,WAAWc,IAAI,CAAC+B;gBAChBD,IAAI,CAACC,EAAEvE,IAAI,CAAC,GAAG0B;YACjB;YAEA,MAAMgD,iBAAiBJ,KAAKK,KAAK,GAAGL,KAAKK,KAAK,CAAC,EAAE,GAAG;YACpD,IAAIA,QAAQ;YACZ,IAAID,gBAAgB;gBAClB,MAAM,EAAErE,QAAQ,EAAE,GAAGqE,eAAezE,KAAK;gBACzC0E,QACE,OAAOtE,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACZA,SAASO,IAAI,CAAC,MACd;YACV;YACA,IAAI+D,UAAUxE,SAASwE,KAAK,EAAExE,SAASwE,KAAK,GAAGA;YAC9C;gBAAC;gBAAQ;gBAAQ;gBAAQ;gBAAS;aAAS,CAACb,OAAO,CAAC,CAAC9D;gBACpDsB,eAAetB,MAAMsE,IAAI,CAACtE,KAAK,IAAI,EAAE;YACvC;QACF;IACF;AACF"}