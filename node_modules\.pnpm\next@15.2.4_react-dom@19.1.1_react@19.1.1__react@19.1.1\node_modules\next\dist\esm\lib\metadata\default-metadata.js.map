{"version": 3, "sources": ["../../../src/lib/metadata/default-metadata.tsx"], "sourcesContent": ["import type {\n  ResolvedMetadata,\n  ResolvedViewport,\n} from './types/metadata-interface'\n\nexport function createDefaultViewport(): ResolvedViewport {\n  return {\n    // name=viewport\n    width: 'device-width',\n    initialScale: 1,\n    // visual metadata\n    themeColor: null,\n    colorScheme: null,\n  }\n}\n\nexport function createDefaultMetadata(): ResolvedMetadata {\n  return {\n    // Deprecated ones\n    viewport: null,\n    themeColor: null,\n    colorScheme: null,\n\n    metadataBase: null,\n    // Other values are all null\n    title: null,\n    description: null,\n    applicationName: null,\n    authors: null,\n    generator: null,\n    keywords: null,\n    referrer: null,\n    creator: null,\n    publisher: null,\n    robots: null,\n    manifest: null,\n    alternates: {\n      canonical: null,\n      languages: null,\n      media: null,\n      types: null,\n    },\n    icons: null,\n    openGraph: null,\n    twitter: null,\n    verification: {},\n    appleWebApp: null,\n    formatDetection: null,\n    itunes: null,\n    facebook: null,\n    abstract: null,\n    appLinks: null,\n    archives: null,\n    assets: null,\n    bookmarks: null,\n    category: null,\n    classification: null,\n    pagination: {\n      previous: null,\n      next: null,\n    },\n    other: {},\n  }\n}\n"], "names": ["createDefaultViewport", "width", "initialScale", "themeColor", "colorScheme", "createDefaultMetadata", "viewport", "metadataBase", "title", "description", "applicationName", "authors", "generator", "keywords", "referrer", "creator", "publisher", "robots", "manifest", "alternates", "canonical", "languages", "media", "types", "icons", "openGraph", "twitter", "verification", "appleWebApp", "formatDetection", "itunes", "facebook", "abstract", "appLinks", "archives", "assets", "bookmarks", "category", "classification", "pagination", "previous", "next", "other"], "mappings": "AAKA,OAAO,SAASA;IACd,OAAO;QACL,gBAAgB;QAChBC,OAAO;QACPC,cAAc;QACd,kBAAkB;QAClBC,YAAY;QACZC,aAAa;IACf;AACF;AAEA,OAAO,SAASC;IACd,OAAO;QACL,kBAAkB;QAClBC,UAAU;QACVH,YAAY;QACZC,aAAa;QAEbG,cAAc;QACd,4BAA4B;QAC5BC,OAAO;QACPC,aAAa;QACbC,iBAAiB;QACjBC,SAAS;QACTC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,SAAS;QACTC,WAAW;QACXC,QAAQ;QACRC,UAAU;QACVC,YAAY;YACVC,WAAW;YACXC,WAAW;YACXC,OAAO;YACPC,OAAO;QACT;QACAC,OAAO;QACPC,WAAW;QACXC,SAAS;QACTC,cAAc,CAAC;QACfC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,YAAY;YACVC,UAAU;YACVC,MAAM;QACR;QACAC,OAAO,CAAC;IACV;AACF"}