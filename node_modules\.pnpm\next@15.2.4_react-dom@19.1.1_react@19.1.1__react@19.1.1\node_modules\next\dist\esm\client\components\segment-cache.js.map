{"version": 3, "sources": ["../../../src/client/components/segment-cache.ts"], "sourcesContent": ["/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const bumpPrefetchTask: typeof import('./segment-cache-impl/scheduler').bumpPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').bumpPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n"], "names": ["notEnabled", "Error", "prefetch", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "args", "require", "navigate", "revalidateEntireCache", "getCurrentCacheVersion", "schedulePrefetchTask", "cancelPrefetchTask", "bumpPrefetchTask", "createCacheKey", "NavigationResultTag", "PrefetchPriority"], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC,GAKD,MAAMA,aAAkB;IACtB,MAAM,qBAEL,CAFK,IAAIC,MACR,uEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,OAAO,MAAMC,WACXC,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,iCAAiCL,QAAQ,IAAII;AAC9D,IACAN,WAAU;AAEhB,OAAO,MAAMQ,WACXL,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,mCAAmCC,QAAQ,IAAIF;AAChE,IACAN,WAAU;AAEhB,OAAO,MAAMS,wBACXN,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,8BAA8BE,qBAAqB,IAC7DH;AAEP,IACAN,WAAU;AAEhB,OAAO,MAAMU,yBACXP,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,8BAA8BG,sBAAsB,IAC9DJ;AAEP,IACAN,WAAU;AAEhB,OAAO,MAAMW,uBACXR,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,kCAAkCI,oBAAoB,IAChEL;AAEP,IACAN,WAAU;AAEhB,OAAO,MAAMY,qBACXT,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,kCAAkCK,kBAAkB,IAC9DN;AAEP,IACAN,WAAU;AAEhB,OAAO,MAAMa,mBACXV,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,kCAAkCM,gBAAgB,IAC5DP;AAEP,IACAN,WAAU;AAEhB,OAAO,MAAMc,iBACXX,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAOC,QAAQ,kCAAkCO,cAAc,IAAIR;AACrE,IACAN,WAAU;AAEhB;;;CAGC,GAED,OAAO,IAAA,AAAWe,6CAAAA;;;;;WAAAA;MAKjB;AAED;;CAEC,GACD,OAAO,IAAA,AAAWC,0CAAAA;IAChB;;;;GAIC;IAED;;GAEC;IAED;;;GAGC;WAdeA;MAgBjB"}