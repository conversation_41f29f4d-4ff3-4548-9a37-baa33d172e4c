"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/site-header.tsx":
/*!************************************!*\
  !*** ./components/site-header.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SiteHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Navigation configuration\nconst navigationConfig = [\n    {\n        name: \"Projects\",\n        role: \"navigation\",\n        href: \"\",\n        hasDropdown: true,\n        dropdownItems: [\n            {\n                name: \"Blockman Legacy\",\n                href: \"https://www.cythro.com/p/blockman-legacy\",\n                role: \"link\"\n            },\n            {\n                name: \"CyltraDash (Coming Soon)\",\n                href: \"#\",\n                role: \"link\"\n            }\n        ]\n    },\n    {\n        name: \"Links\",\n        role: \"link\",\n        href: \"\",\n        hasDropdown: true,\n        dropdownItems: [\n            {\n                name: \"Blog\",\n                href: \"https://blog.cythro.com\",\n                role: \"link\"\n            },\n            {\n                name: \"Status\",\n                href: \"\",\n                role: \"link\"\n            },\n            {\n                name: \"Brandingn\",\n                href: \"#\",\n                role: \"link\"\n            }\n        ]\n    },\n    {\n        name: \"About\",\n        role: \"link\",\n        href: \"#about\",\n        hasDropdown: false\n    },\n    {\n        name: \"Contact\",\n        role: \"link\",\n        href: \"#contact\",\n        hasDropdown: false\n    }\n];\nfunction SiteHeader() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [projectsDropdownOpen, setProjectsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [hoverTimeout, setHoverTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Handle mouse enter for dropdown\n    const handleMouseEnter = ()=>{\n        if (hoverTimeout) {\n            clearTimeout(hoverTimeout);\n            setHoverTimeout(null);\n        }\n        setProjectsDropdownOpen(true);\n    };\n    // Handle mouse leave for dropdown\n    const handleMouseLeave = ()=>{\n        const timeout = setTimeout(()=>{\n            setProjectsDropdownOpen(false);\n        }, 150) // Small delay to prevent flickering\n        ;\n        setHoverTimeout(timeout);\n    };\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SiteHeader.useEffect\": ()=>{\n            return ({\n                \"SiteHeader.useEffect\": ()=>{\n                    if (hoverTimeout) {\n                        clearTimeout(hoverTimeout);\n                    }\n                }\n            })[\"SiteHeader.useEffect\"];\n        }\n    }[\"SiteHeader.useEffect\"], [\n        hoverTimeout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-7xl px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"glass-ultra rounded-3xl px-8 py-4 border border-white/20 shadow-glass\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 md:w-16 md:h-16 rounded-xl md:rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/cythro.png\",\n                                            alt: \"Cythro Logo\",\n                                            className: \"w-full h-full object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl md:text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent\",\n                                            children: \"Cythro\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-xs font-medium -mt-1 hidden sm:block\",\n                                            children: \"Digital Creators\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationConfig.map((item)=>{\n                                var _item_dropdownItems;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: handleMouseEnter,\n                                        onMouseLeave: handleMouseLeave,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                role: item.role,\n                                                onClick: ()=>setProjectsDropdownOpen(!projectsDropdownOpen),\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 transition-transform duration-200 \".concat(projectsDropdownOpen ? 'rotate-180' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 21\n                                            }, this),\n                                            projectsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-48 dropdown-glass rounded-2xl border border-white/20 shadow-glass overflow-hidden z-50\",\n                                                children: (_item_dropdownItems = item.dropdownItems) === null || _item_dropdownItems === void 0 ? void 0 : _item_dropdownItems.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: dropdownItem.href,\n                                                        role: dropdownItem.role,\n                                                        className: \"block px-4 py-3 text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0\",\n                                                        onClick: ()=>setProjectsDropdownOpen(false),\n                                                        children: dropdownItem.name\n                                                    }, dropdownItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        role: item.role,\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"hidden md:inline-flex text-gray-300 hover:text-white hover:bg-white/10 glass-button\",\n                                    children: \"Say Hello\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"sm\",\n                                    className: \"premium-button hidden sm:inline-flex\",\n                                    children: [\n                                        \"Let's Work Together\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-2 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"sm\",\n                                    className: \"premium-button sm:hidden\",\n                                    children: [\n                                        \"Work Together\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-1 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"lg:hidden text-gray-300 hover:text-white p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 33\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 61\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden mt-6 pt-6 border-t border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mobile-dropdown rounded-2xl p-6 -mx-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6\",\n                            children: [\n                                navigationConfig.map((item)=>{\n                                    var _item_dropdownItems;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    role: item.role,\n                                                    onClick: ()=>setProjectsDropdownOpen(!projectsDropdownOpen),\n                                                    className: \"w-full text-left text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5 transition-transform duration-200 \".concat(projectsDropdownOpen ? 'rotate-180' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 25\n                                                }, this),\n                                                projectsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 ml-4 space-y-2\",\n                                                    children: (_item_dropdownItems = item.dropdownItems) === null || _item_dropdownItems === void 0 ? void 0 : _item_dropdownItems.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: dropdownItem.href,\n                                                            role: dropdownItem.role,\n                                                            className: \"block text-gray-300 hover:text-white transition-colors duration-300 font-medium py-2 pl-4 border-l-2 border-white/20\",\n                                                            onClick: ()=>{\n                                                                setMobileMenuOpen(false);\n                                                                setProjectsDropdownOpen(false);\n                                                            },\n                                                            children: dropdownItem.name\n                                                        }, dropdownItem.name, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            role: item.role,\n                                            className: \"text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 last:border-b-0 block\",\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"lg\",\n                                            className: \"w-full premium-button mb-3\",\n                                            children: [\n                                                \"Let's Work Together\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"lg\",\n                                            className: \"w-full text-white hover:text-gray-300 hover:bg-white/10 glass-button\",\n                                            children: \"Say Hello\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(SiteHeader, \"LZma9/y0lmhJgvOvaa2hnJhRwHc=\");\n_c = SiteHeader;\nvar _c;\n$RefreshReg$(_c, \"SiteHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/site-header.tsx\n"));

/***/ })

});