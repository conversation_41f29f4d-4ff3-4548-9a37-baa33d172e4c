"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/site-header.tsx":
/*!************************************!*\
  !*** ./components/site-header.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SiteHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,ChevronDown,Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Navigation configuration\nconst navigationConfig = [\n    {\n        name: \"Projects\",\n        role: \"navigation\",\n        href: \"\",\n        hasDropdown: true,\n        dropdownItems: [\n            {\n                name: \"Blockman Legacy\",\n                href: \"/p/blockman-legacy\",\n                role: \"link\"\n            },\n            {\n                name: \"CyltraDash (Coming Soon)\",\n                href: \"#\",\n                role: \"link\"\n            }\n        ]\n    },\n    {\n        name: \"Links\",\n        role: \"link\",\n        href: \"\",\n        hasDropdown: true,\n        dropdownItems: [\n            {\n                name: \"Blog\",\n                href: \"https://blog.cythro.com\",\n                role: \"link\"\n            },\n            {\n                name: \"Status\",\n                href: \"https://status.cythro.com\",\n                role: \"link\"\n            },\n            {\n                name: \"Branding\",\n                href: \"/branding\",\n                role: \"link\"\n            }\n        ]\n    },\n    {\n        name: \"Contact\",\n        role: \"link\",\n        href: \"\",\n        hasDropdown: true,\n        dropdownItems: [\n            {\n                name: \"Discord\",\n                href: \"#\",\n                role: \"link\"\n            },\n            {\n                name: \"\",\n                href: \"#\",\n                role: \"link\"\n            }\n        ]\n    },\n    {\n        name: \"Contact\",\n        role: \"link\",\n        href: \"#contact\",\n        hasDropdown: false\n    }\n];\nfunction SiteHeader() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [projectsDropdownOpen, setProjectsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [hoverTimeout, setHoverTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Handle mouse enter for dropdown\n    const handleMouseEnter = ()=>{\n        if (hoverTimeout) {\n            clearTimeout(hoverTimeout);\n            setHoverTimeout(null);\n        }\n        setProjectsDropdownOpen(true);\n    };\n    // Handle mouse leave for dropdown\n    const handleMouseLeave = ()=>{\n        const timeout = setTimeout(()=>{\n            setProjectsDropdownOpen(false);\n        }, 150) // Small delay to prevent flickering\n        ;\n        setHoverTimeout(timeout);\n    };\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SiteHeader.useEffect\": ()=>{\n            return ({\n                \"SiteHeader.useEffect\": ()=>{\n                    if (hoverTimeout) {\n                        clearTimeout(hoverTimeout);\n                    }\n                }\n            })[\"SiteHeader.useEffect\"];\n        }\n    }[\"SiteHeader.useEffect\"], [\n        hoverTimeout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-7xl px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"glass-ultra rounded-3xl px-8 py-4 border border-white/20 shadow-glass\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 md:w-16 md:h-16 rounded-xl md:rounded-2xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/cythro.png\",\n                                            alt: \"Cythro Logo\",\n                                            className: \"w-full h-full object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl md:text-2xl font-black bg-gradient-to-r from-[#780206] via-pink-400 to-[#061161] bg-clip-text text-transparent\",\n                                            children: \"Cythro\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-xs font-medium -mt-1 hidden sm:block\",\n                                            children: \"Digital Creators\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationConfig.map((item)=>{\n                                var _item_dropdownItems;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: handleMouseEnter,\n                                        onMouseLeave: handleMouseLeave,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                role: item.role,\n                                                onClick: ()=>setProjectsDropdownOpen(!projectsDropdownOpen),\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 transition-transform duration-200 \".concat(projectsDropdownOpen ? 'rotate-180' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 21\n                                            }, this),\n                                            projectsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-48 dropdown-glass rounded-2xl border border-white/20 shadow-glass overflow-hidden z-50\",\n                                                children: (_item_dropdownItems = item.dropdownItems) === null || _item_dropdownItems === void 0 ? void 0 : _item_dropdownItems.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: dropdownItem.href,\n                                                        role: dropdownItem.role,\n                                                        className: \"block px-4 py-3 text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0\",\n                                                        onClick: ()=>setProjectsDropdownOpen(false),\n                                                        children: dropdownItem.name\n                                                    }, dropdownItem.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        role: item.role,\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-300 font-medium relative group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#780206] to-[#061161] group-hover:w-full transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"hidden md:inline-flex text-gray-300 hover:text-white hover:bg-white/10 glass-button\",\n                                    children: \"Say Hello\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"sm\",\n                                    className: \"premium-button hidden sm:inline-flex\",\n                                    children: [\n                                        \"Let's Work Together\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-2 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"sm\",\n                                    className: \"premium-button sm:hidden\",\n                                    children: [\n                                        \"Work Together\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"ml-1 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"lg:hidden text-gray-300 hover:text-white p-2\",\n                                    onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                    children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 33\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 61\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden mt-6 pt-6 border-t border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mobile-dropdown rounded-2xl p-6 -mx-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-6\",\n                            children: [\n                                navigationConfig.map((item)=>{\n                                    var _item_dropdownItems;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    role: item.role,\n                                                    onClick: ()=>setProjectsDropdownOpen(!projectsDropdownOpen),\n                                                    className: \"w-full text-left text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5 transition-transform duration-200 \".concat(projectsDropdownOpen ? 'rotate-180' : '')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 25\n                                                }, this),\n                                                projectsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 ml-4 space-y-2\",\n                                                    children: (_item_dropdownItems = item.dropdownItems) === null || _item_dropdownItems === void 0 ? void 0 : _item_dropdownItems.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: dropdownItem.href,\n                                                            role: dropdownItem.role,\n                                                            className: \"block text-gray-300 hover:text-white transition-colors duration-300 font-medium py-2 pl-4 border-l-2 border-white/20\",\n                                                            onClick: ()=>{\n                                                                setMobileMenuOpen(false);\n                                                                setProjectsDropdownOpen(false);\n                                                            },\n                                                            children: dropdownItem.name\n                                                        }, dropdownItem.name, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 31\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            role: item.role,\n                                            className: \"text-white hover:text-gray-300 transition-colors duration-300 font-semibold text-lg py-2 border-b border-white/10 last:border-b-0 block\",\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"lg\",\n                                            className: \"w-full premium-button mb-3\",\n                                            children: [\n                                                \"Let's Work Together\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"lg\",\n                                            className: \"w-full text-white hover:text-gray-300 hover:bg-white/10 glass-button\",\n                                            children: \"Say Hello\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cythro Workspace\\\\CythroWebsite\\\\components\\\\site-header.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(SiteHeader, \"LZma9/y0lmhJgvOvaa2hnJhRwHc=\");\n_c = SiteHeader;\nvar _c;\n$RefreshReg$(_c, \"SiteHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/site-header.tsx\n"));

/***/ })

});