{"version": 3, "sources": ["../../../../../src/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.ts"], "sourcesContent": ["import {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type HMR_ACTION_TYPES,\n} from '../../../../server/dev/hot-reloader-types'\nimport { devBuildIndicator } from './dev-build-indicator'\n\n/**\n * Handles HMR events to control the dev build indicator visibility.\n * Shows indicator when building and hides it when build completes or syncs.\n */\nexport const handleDevBuildIndicatorHmrEvents = (obj: HMR_ACTION_TYPES) => {\n  try {\n    if (!('action' in obj)) {\n      return\n    }\n\n    // eslint-disable-next-line default-case\n    switch (obj.action) {\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n        devBuildIndicator.show()\n        break\n      case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n      case HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n        devBuildIndicator.hide()\n        break\n    }\n  } catch {}\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER", "devBuildIndicator", "handleDevBuildIndicatorHmrEvents", "obj", "action", "BUILDING", "show", "BUILT", "SYNC", "hide"], "mappings": "AAAA,SACEA,2BAA2B,QAEtB,4CAA2C;AAClD,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD;;;CAGC,GACD,OAAO,MAAMC,mCAAmC,CAACC;IAC/C,IAAI;QACF,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;YACtB;QACF;QAEA,wCAAwC;QACxC,OAAQA,IAAIC,MAAM;YAChB,KAAKJ,4BAA4BK,QAAQ;gBACvCJ,kBAAkBK,IAAI;gBACtB;YACF,KAAKN,4BAA4BO,KAAK;YACtC,KAAKP,4BAA4BQ,IAAI;gBACnCP,kBAAkBQ,IAAI;gBACtB;QACJ;IACF,EAAE,UAAM,CAAC;AACX,EAAC"}