{"version": 3, "sources": ["../../src/lib/setup-exception-listeners.ts"], "sourcesContent": ["process.on('uncaughtException', (err) => {\n  console.error('uncaughtException', err)\n  process.exit(1)\n})\n\nprocess.on('unhandledRejection', (err) => {\n  console.error('unhandledRejection', err)\n  process.exit(1)\n})\n"], "names": ["process", "on", "err", "console", "error", "exit"], "mappings": "AAAAA,QAAQC,EAAE,CAAC,qBAAqB,CAACC;IAC/BC,QAAQC,KAAK,CAAC,qBAAqBF;IACnCF,QAAQK,IAAI,CAAC;AACf;AAEAL,QAAQC,EAAE,CAAC,sBAAsB,CAACC;IAChCC,QAAQC,KAAK,CAAC,sBAAsBF;IACpCF,QAAQK,IAAI,CAAC;AACf"}