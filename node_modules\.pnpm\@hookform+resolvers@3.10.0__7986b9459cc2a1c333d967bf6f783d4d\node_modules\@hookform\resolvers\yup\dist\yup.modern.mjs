import{validateFieldsNatively as e,toNestErrors as t}from"@hookform/resolvers";import{appendErrors as o}from"react-hook-form";function r(r,a={},s={}){return async(n,c,i)=>{try{a.context&&"development"===process.env.NODE_ENV&&console.warn("You should not used the yup options context. Please, use the 'useForm' context object instead");const t=await r["sync"===s.mode?"validateSync":"validate"](n,Object.assign({abortEarly:!1},a,{context:c}));return i.shouldUseNativeValidation&&e({},i),{values:s.raw?n:t,errors:{}}}catch(e){if(!e.inner)throw e;return{values:{},errors:t((p=e,l=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,(p.inner||[]).reduce((e,t)=>{if(e[t.path]||(e[t.path]={message:t.message,type:t.type}),l){const r=e[t.path].types,a=r&&r[t.type];e[t.path]=o(t.path,l,e,t.type,a?[].concat(a,t.message):t.message)}return e},{})),i)}}var p,l}}export{r as yupResolver};
//# sourceMappingURL=yup.modern.mjs.map
